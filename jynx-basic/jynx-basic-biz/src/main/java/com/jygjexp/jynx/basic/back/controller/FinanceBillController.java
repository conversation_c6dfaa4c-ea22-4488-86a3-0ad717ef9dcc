package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.model.bo.FinanceBillExportBo;
import com.jygjexp.jynx.basic.back.model.bo.FinanceBillPageBo;
import com.jygjexp.jynx.basic.back.model.dto.FinanceBillPageDto;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.FinanceBillEntity;
import com.jygjexp.jynx.basic.back.service.FinanceBillService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 财务账单
 *
 * <AUTHOR>
 * @date 2024-11-15 16:34:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/financeBill")
@Tag(description = "financeBill", name = "驿站月账单")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class FinanceBillController {

    private final FinanceBillService financeBillService;

    /**
     * 分页查询
     *
     * @param page        分页对象
     * @param financeBill 财务账单
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    public R getFinanceBillPage(@ParameterObject Page page, @ParameterObject FinanceBillPageBo financeBill) {
        FinanceBillPageDto dto = new FinanceBillPageDto();
        BeanUtils.copyProperties(financeBill, dto);
        return R.ok(financeBillService.getPage(page, dto));
    }

    @Operation(summary = "通过驿站ID查询", description = "通过驿站ID查询")
    @GetMapping("/getByPostId")
    public R getByPostId(@RequestParam Integer postId) {
        return R.ok(financeBillService.getByPostId(postId));
    }


    /**
     * 通过id查询财务账单
     *
     * @param billId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{billId}")
    @PreAuthorize("@pms.hasPermission('back_FinanceBill_view')")
    public R getById(@PathVariable("billId") Integer billId) {
        return R.ok(financeBillService.getById(billId));
    }

    /**
     * 新增财务账单
     *
     * @param financeBill 财务账单
     * @return R
     */
    @Operation(summary = "新增财务账单", description = "新增财务账单")
    @SysLog("新增财务账单")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_FinanceBill_add')")
    public R save(@RequestBody FinanceBillEntity financeBill) {
        return R.ok(financeBillService.save(financeBill));
    }

    /**
     * 修改财务账单
     *
     * @param financeBill 财务账单
     * @return R
     */
    @Operation(summary = "修改财务账单", description = "修改财务账单")
    @SysLog("修改财务账单")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_FinanceBill_edit')")
    public R updateById(@RequestBody FinanceBillEntity financeBill) {
        return R.ok(financeBillService.updateById(financeBill));
    }

    /**
     * 通过id删除财务账单
     *
     * @param ids billId列表
     * @return R
     */
    @Operation(summary = "通过id删除财务账单", description = "通过id删除财务账单")
    @SysLog("通过id删除财务账单")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_FinanceBill_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(financeBillService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格(模板导出)
     *
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/exportTemplate")
    @PreAuthorize("@pms.hasPermission('back_FinanceBill_export')")
    public void export(@RequestParam String billId, @RequestParam Integer postId, @RequestParam String time, HttpServletResponse response) {
        financeBillService.getExcel(billId, postId, time, response);
    }


    /**
     * 条件导出excel 表格(模板导出)
     *
     * @return excel 文件流
     */
    @Operation(summary = "条件导出excel 表格", description = "条件导出excel 表格")
    @ResponseExcel
    @GetMapping("/exportFinanceBill")
    @PreAuthorize("@pms.hasPermission('back_FinanceBill_export2')")
    public List<FinanceBillExportBo> exportFinanceBill(FinanceBillPageBo vo, Integer[] ids) {
        return financeBillService.getExcel2(vo,ids);
    }


}