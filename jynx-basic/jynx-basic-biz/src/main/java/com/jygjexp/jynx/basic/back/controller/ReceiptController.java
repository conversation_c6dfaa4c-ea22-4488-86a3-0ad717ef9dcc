package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.model.bo.ReceiptEntityPageBo;
import com.jygjexp.jynx.basic.back.model.vo.excel.ReceiptExcelVo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.ReceiptEntity;
import com.jygjexp.jynx.basic.back.service.ReceiptService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 收款
 *
 * <AUTHOR>
 * @date 2024-11-20 10:29:39
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/receipt")
@Tag(description = "receipt", name = "合作商月账单")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ReceiptController {

    private final ReceiptService receiptService;

    /**
     * 分页查询
     *
     * @param page    分页对象
     * @param receipt 收款
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_Receipt_view')")
    public R getReceiptPage(@ParameterObject Page page, @ParameterObject ReceiptEntityPageBo receipt) {
        return R.ok(receiptService.getPage(page, receipt));
    }


    /**
     * 通过id查询收款
     *
     * @param receiptId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{receiptId}")
    @PreAuthorize("@pms.hasPermission('back_Receipt_view')")
    public R getById(@PathVariable("receiptId") Integer receiptId) {
        return R.ok(receiptService.getById(receiptId));
    }

    /**
     * 新增收款
     *
     * @param receipt 收款
     * @return R
     */
    @Operation(summary = "新增收款", description = "新增收款")
    @SysLog("新增收款")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_Receipt_add')")
    public R save(@RequestBody ReceiptEntity receipt) {
        return R.ok(receiptService.save(receipt));
    }

    /**
     * 修改收款
     *
     * @param receipt 收款
     * @return R
     */
    @Operation(summary = "修改收款", description = "修改收款")
    @SysLog("修改收款")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_Receipt_edit')")
    public R updateById(@RequestBody ReceiptEntity receipt) {
        return R.ok(receiptService.updateById(receipt));
    }

    /**
     * 通过id删除收款
     *
     * @param ids receiptId列表
     * @return R
     */
    @Operation(summary = "通过id删除收款", description = "通过id删除收款")
    @SysLog("通过id删除收款")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_Receipt_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(receiptService.removeBatchByIds(CollUtil.toList(ids)));
    }

    @Operation(summary = "获取账单明细", description = "获取账单明细")
    @SysLog("获取账单明细")
    @GetMapping("/getDetail")
    @PreAuthorize("@pms.hasPermission('back_Receipt_view')")
    public R getDetail(@ParameterObject Page page, Integer authId, String time) {
        return R.ok(receiptService.getDetail(page, authId, time));
    }


    /**
     * 导出excel 表格
     *
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_Receipt_export')")
    public List<ReceiptExcelVo> export(Integer authId, String time) {
        return receiptService.getExcel(authId, time);
    }



    /**
     * 模板导出
     *
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/exportTemplate")
    @PreAuthorize("@pms.hasPermission('back_Receipt_export')")
    public void export(HttpServletResponse response,@RequestParam Integer receiptId) {
      receiptService.exportTemplate(response,receiptId);
    }
}