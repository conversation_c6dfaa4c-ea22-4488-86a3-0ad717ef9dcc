package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.model.bo.PudoOrderPageBo;
import com.jygjexp.jynx.basic.back.model.vo.excel.PudoExcelVo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.PudoOrderEntity;
import com.jygjexp.jynx.basic.back.service.PudoOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * PUDO返仓列表
 *
 * <AUTHOR>
 * @date 2024-11-18 17:47:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/pudoOrder")
@Tag(description = "pudoOrder", name = "PUDO返仓列表")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PudoOrderController {

    private final PudoOrderService pudoOrderService;

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param pudoOrder PUDO返仓列表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_PudoOrder_view')" )
    public R getPudoOrderPage(@ParameterObject Page page, @ParameterObject PudoOrderPageBo pudoOrder) {
        return R.ok(pudoOrderService.getPage(page, pudoOrder));
    }


    /**
     * 通过id查询PUDO返仓列表
     *
     * @param orderId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{orderId}")
    @PreAuthorize("@pms.hasPermission('back_PudoRecord_view')")
    public R getById(@PathVariable("orderId") Integer orderId) {
        return R.ok(pudoOrderService.getById(orderId));
    }

    /**
     * 新增PUDO返仓列表
     *
     * @param pudoOrder PUDO返仓列表
     * @return R
     */
    @Operation(summary = "新增PUDO返仓列表", description = "新增PUDO返仓列表")
    @SysLog("新增PUDO返仓列表")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_PudoRecord_add')")
    public R save(@RequestBody PudoOrderEntity pudoOrder) {
        return R.ok(pudoOrderService.save(pudoOrder));
    }

    /**
     * 修改PUDO返仓列表
     *
     * @param pudoOrder PUDO返仓列表
     * @return R
     */
    @Operation(summary = "修改PUDO返仓列表", description = "修改PUDO返仓列表")
    @SysLog("修改PUDO返仓列表")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_PudoRecord_edit')")
    public R updateById(@RequestBody PudoOrderEntity pudoOrder) {
        return R.ok(pudoOrderService.updateById(pudoOrder));
    }

    /**
     * 通过id删除PUDO返仓列表
     *
     * @param ids orderId列表
     * @return R
     */
    @Operation(summary = "通过id删除PUDO返仓列表", description = "通过id删除PUDO返仓列表")
    @SysLog("通过id删除PUDO返仓列表")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_PudoRecord_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(pudoOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param pudoOrder 查询条件
     * @param ids       导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_PudoRecord_export')")
    public List<PudoExcelVo> export(PudoOrderPageBo pudoOrder, Integer[] ids) {
        return pudoOrderService.getExcel(pudoOrder, ids);
    }

}
