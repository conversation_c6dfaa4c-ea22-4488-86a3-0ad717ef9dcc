package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.GmapGeocodeEntity;
import com.jygjexp.jynx.basic.back.service.GmapGeocodeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 谷歌地图数据缓存
 *
 * <AUTHOR>
 * @date 2024-10-11 21:47:05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/gmapGeocode" )
@Tag(description = "gmapGeocode" , name = "谷歌地图数据缓存管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class GmapGeocodeController {

    private final  GmapGeocodeService gmapGeocodeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param gmapGeocode 谷歌地图数据缓存
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('back_GmapGeocode_view')" )
    public R getGmapGeocodePage(@ParameterObject Page page, @ParameterObject GmapGeocodeEntity gmapGeocode) {
        LambdaQueryWrapper<GmapGeocodeEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(gmapGeocodeService.page(page, wrapper));
    }


    /**
     * 通过id查询谷歌地图数据缓存
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('back_GmapGeocode_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(gmapGeocodeService.getById(id));
    }

    /**
     * 新增谷歌地图数据缓存
     * @param gmapGeocode 谷歌地图数据缓存
     * @return R
     */
    @Operation(summary = "新增谷歌地图数据缓存" , description = "新增谷歌地图数据缓存" )
    @SysLog("新增谷歌地图数据缓存" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_GmapGeocode_add')" )
    public R save(@RequestBody GmapGeocodeEntity gmapGeocode) {
        return R.ok(gmapGeocodeService.save(gmapGeocode));
    }

    /**
     * 新增谷歌地图数据缓存
     *
     * @return Boolean
     */
    @Operation(summary = "新增谷歌地图数据缓存-tms" , description = "新增谷歌地图数据缓存-tms" )
    @PostMapping("/addGmapResult")
    public Boolean addGmapResult(@RequestBody Map<String, String> requestBody) {
        String keyword = requestBody.get("keyword");
        String result = requestBody.get("result");
        return gmapGeocodeService.addGmapResult(keyword, result);
    }

    /**
     * 修改谷歌地图数据缓存
     * @param gmapGeocode 谷歌地图数据缓存
     * @return R
     */
    @Operation(summary = "修改谷歌地图数据缓存" , description = "修改谷歌地图数据缓存" )
    @SysLog("修改谷歌地图数据缓存" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_GmapGeocode_edit')" )
    public R updateById(@RequestBody GmapGeocodeEntity gmapGeocode) {
        return R.ok(gmapGeocodeService.updateById(gmapGeocode));
    }

    /**
     * 通过id删除谷歌地图数据缓存
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除谷歌地图数据缓存" , description = "通过id删除谷歌地图数据缓存" )
    @SysLog("通过id删除谷歌地图数据缓存" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_GmapGeocode_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(gmapGeocodeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param gmapGeocode 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_GmapGeocode_export')" )
    public List<GmapGeocodeEntity> export(GmapGeocodeEntity gmapGeocode,Integer[] ids) {
        return gmapGeocodeService.list(Wrappers.lambdaQuery(gmapGeocode).in(ArrayUtil.isNotEmpty(ids), GmapGeocodeEntity::getId, ids));
    }
}