package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.AdminEntity;
import com.jygjexp.jynx.basic.back.service.AdminService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 运营人员
 *
 * <AUTHOR>
 * @date 2024-11-20 17:51:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin" )
@Tag(description = "admin" , name = "运营人员管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AdminController {

    private final  AdminService adminService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param admin 运营人员
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('back_Admin_view')" )
    public R getAdminPage(@ParameterObject Page page, @ParameterObject AdminEntity admin) {
        LambdaQueryWrapper<AdminEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(adminService.page(page, wrapper));
    }


    /**
     * 通过id查询运营人员
     * @param adminId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{adminId}" )
    @PreAuthorize("@pms.hasPermission('back_Admin_view')" )
    public R getById(@PathVariable("adminId" ) Integer adminId) {
        return R.ok(adminService.getById(adminId));
    }

    /**
     * 新增运营人员
     * @param admin 运营人员
     * @return R
     */
    @Operation(summary = "新增运营人员" , description = "新增运营人员" )
    @SysLog("新增运营人员" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_Admin_add')" )
    public R save(@RequestBody AdminEntity admin) {
        return R.ok(adminService.save(admin));
    }

    /**
     * 修改运营人员
     * @param admin 运营人员
     * @return R
     */
    @Operation(summary = "修改运营人员" , description = "修改运营人员" )
    @SysLog("修改运营人员" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_Admin_edit')" )
    public R updateById(@RequestBody AdminEntity admin) {
        return R.ok(adminService.updateById(admin));
    }

    /**
     * 通过id删除运营人员
     * @param ids adminId列表
     * @return R
     */
    @Operation(summary = "通过id删除运营人员" , description = "通过id删除运营人员" )
    @SysLog("通过id删除运营人员" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_Admin_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(adminService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param admin 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_Admin_export')" )
    public List<AdminEntity> export(AdminEntity admin,Integer[] ids) {
        return adminService.list(Wrappers.lambdaQuery(admin).in(ArrayUtil.isNotEmpty(ids), AdminEntity::getAdminId, ids));
    }
}