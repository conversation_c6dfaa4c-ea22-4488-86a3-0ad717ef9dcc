package com.jygjexp.jynx.basic.back.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jygjexp.jynx.basic.back.api.feign.RemoteTmsService;
import com.jygjexp.jynx.basic.back.dto.*;
import com.jygjexp.jynx.basic.back.entity.SheinCodeEntity;
import com.jygjexp.jynx.basic.back.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.basic.back.entity.TmsSortingRecordEntity;
import com.jygjexp.jynx.basic.back.enums.BusinessType;
import com.jygjexp.jynx.basic.back.enums.SortingRuleType;
import com.jygjexp.jynx.basic.back.exception.CustomBusinessException;
import com.jygjexp.jynx.basic.back.model.dto.SortingDTO;
import com.jygjexp.jynx.basic.back.model.vo.SortingVO;
import com.jygjexp.jynx.basic.back.request.ConditionGroup;
import com.jygjexp.jynx.basic.back.request.Order;
import com.jygjexp.jynx.basic.back.service.OrderService;
import com.jygjexp.jynx.basic.back.service.SortingService;
import com.jygjexp.jynx.basic.back.tools.BuildSpElUtil;
import com.jygjexp.jynx.basic.back.tools.GlobalDeliveryCircularQueue;
import com.jygjexp.jynx.basic.back.tools.GlobalReturnCircularQueue;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SortingServiceImpl implements SortingService {
    private static final Logger logger = LoggerFactory.getLogger(SortingServiceImpl.class);
    private final OrderService orderService;
    private final RemoteTmsService remoteTmsService;
    private final ApplicationContext applicationContext;
    private static final List<String> SUPPORTED_ORDER_PREFIXES = Arrays.asList("N", "NR", "PUD","U9999","GV","JY","UNI");
    // 客户单号转映射
    private static final List<String> SUPPORTED_PACKAGE_PREFIXES = Arrays.asList("U9999","GV","JY","UNI");
    // 匹配规则: 前面 7位 （数字+字母mix的） + 16位数单号 + 00000 （5个零） 兼容大小写
    private static final Pattern HALF_TRUSTEESHIP_PATTERN = Pattern.compile("^[A-Za-z0-9]{7}(\\d{16})00000$");
    // 半托管提货 佳邮:JY; Purolator:开头是FZX,KRO; CP:开头是1029/2019/4008
    private static final List<String> HALF_TRUSTEESHIP_ORDER_PREFIXES = Arrays.asList("JY","FZX","KRO","1029","2019","4008");
    // 派送单标识字符位置
    private static final int DELIVERY_ORDER_CHAR_INDEX = 5;
    private static final char DELIVERY_ORDER_CHAR = 'D';
    // 授权密钥
    private static final String AUTHORIZATION_KEY = "8F3kZxT1vR7uQa92LmDgWbEYt6PjNC4o";
    // PUD包裹的固定商家ID
    private static final Integer PUD_MERCHANT_ID = 100;
    // 服务商名称 -固定
    private static final String PROVIDER_NAME = "NB";
    // 轮转指针
    AtomicInteger lastIndex = new AtomicInteger(-1);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SortingVO sorting(SortingDTO sortingDTO) {
        if(logger.isInfoEnabled()){
            logger.info("开始处理分拣请求，请求数据:{},开始时间:{}", sortingDTO,LocalDateTime.now());
        }
        // 1. 授权验证 接口未验证 不支持处理逻辑
        validateAuthorization(sortingDTO);
        List<String> orderNumber = sortingDTO.getOrderNumber();
        if(CollUtil.isEmpty(orderNumber)){
            throw new CustomBusinessException("单号列表不能为空");
        }
        // 2.模板拦截 :半托管类型模板,只记录扫码日志 / 非半托管 正常走原来逻辑
        TmsSortingTemplateDto tmsSortingTemplateDto = validateAndGetSortingTemplate();
        Integer businessType = tmsSortingTemplateDto.getBusinessType();

        SortingVO sortingVO = new SortingVO();
        TmsSortingRecordEntity sortingRecord = new TmsSortingRecordEntity();
        // 记录处理过程中的关键数据
        SortingContext context = new SortingContext();
        // 异步处理对象
        SortingServiceImpl proxy = applicationContext.getBean(SortingServiceImpl.class);
        // 半托管处理
        if(BusinessType.HALF_TRUSTEESHIP.getCode().equals(businessType)){
            List<Long> gridIds = tmsSortingTemplateDto.getGridIds();
            if(CollUtil.isEmpty(gridIds)){
                throw new CustomBusinessException("未找到匹配的格口");
            }
            List<Long> sortGridIds = gridIds.stream().sorted().collect(Collectors.toList());
            int nextIndex = (lastIndex.getAndIncrement() + 1) % sortGridIds.size();
            Long gridId = sortGridIds.get(nextIndex);

            context.isSuccess = true;
            context.template = tmsSortingTemplateDto;
            context.sortingRuleType = context.template.getSortingRuleType();
            processHalfOrderNumbers(context,orderNumber);
            markHalfMainFlag(context);

            sortingVO.setCode(200);
            sortingVO.setGridCode(gridId.intValue());
            sortingVO.setMsg("半托管分拣成功");
            // 7. 后置处理 - 异步保存半托管扫描记录
            proxy.saveSortingRecordAsync(sortingRecord, context, sortingDTO, sortingVO);
        }else{
            // 前置异步处理 - 记录日志&订单状态变更
            List<String> deliveryOrders = getDeliveryOrders(sortingDTO);
            proxy.sortOperatedAsync(deliveryOrders);
            try {
                // 2. 前置验证
                context.orderNo = validateAndGetOrderNo(sortingDTO);
                context.template = tmsSortingTemplateDto;
                context.sortingRuleType = context.template.getSortingRuleType();
                context.deliveryQueue = GlobalDeliveryCircularQueue.getInstance();
                context.returnQueue = GlobalReturnCircularQueue.getInstance();
                context.deliveryGridList = new ArrayList<>();
                context.returnGridList = new ArrayList<>();
                // 3. 业务类型兼容性检查
                validateBusinessTypeCompatibility(context.template, context.orderNo);
                // 4. 核心分拣逻辑
                int gridCode = processSorting(context);
                // 5. 设置返回结果
                sortingVO.setCode(200);
                sortingVO.setMsg("分拣成功");
                sortingVO.setGridCode(gridCode);
                context.isSuccess = true;
                if(logger.isInfoEnabled()){
                    logger.info("分拣成功，单号: {}, 格口号: {}", context.orderNo, gridCode);
                }
            } catch (CustomBusinessException e) {
                handleBusinessException(sortingVO, context, e);
            } catch (Exception e) {
                handleUnexpectedException(sortingVO, context, e);
            } finally {
                // 6. 后置处理 - 异步保存分拣记录
                proxy.saveSortingRecordAsync(sortingRecord, context, sortingDTO, sortingVO);
                // 将订单保存到批次信息中
                proxy.saveOrderBatchAsync(businessType, context.orderNo);
            }
        }
        if(logger.isInfoEnabled()){
            logger.info("处理分拣请求结束，请求数据:{},结束时间:{}", sortingDTO,LocalDateTime.now());
        }
        return sortingVO;
    }


    @Async
    public void saveOrderBatchAsync(Integer businessType, String orderNo) {
        if (BusinessType.DELIVERY.getCode().equals(businessType)){
            remoteTmsService.saveBatchOrder(orderNo);
        }
    }

    @Override
    @Async
    public void sortOperatedAsync(List<String> entrustedOrderNumbers) {
        if(CollUtil.isEmpty(entrustedOrderNumbers)){
            return;
        }
        remoteTmsService.sortingOperated(entrustedOrderNumbers);
    }

    @Override
    @Async
    public void sortCalculatePriceAsync(PriceCalculationDTO priceCalculationDTO) {
        if(null == priceCalculationDTO){
            return;
        }
        List<String> orders = priceCalculationDTO.getOrders();
        if(CollUtil.isEmpty(orders)){
            return;
        }
        remoteTmsService.calculatePrice(priceCalculationDTO);
    }

    /**
     * 异步保存分拣记录
     */
    @Async
    public void saveSortingRecordAsync(TmsSortingRecordEntity record, SortingContext context,
                                       SortingDTO sortingDTO, SortingVO sortingVO) {
        try {
            // 设置基本信息
            BusinessType lookup = BusinessType.lookup(context.template.getBusinessType());
            record.setType(lookup.getCode());
            record.setScanTime(LocalDateTime.now());
            record.setTemplateName(context.template.getTemplateName());
            record.setTemplateId(context.template.getId());
            record.setGrid(sortingVO.getGridCode());
            record.setOrderNo(context.orderNo);
            record.setBillOrderNo(context.billOrderNo);
            record.setMainFlag(context.mainFlag);
            record.setExtend(JSON.toJSONString(sortingDTO));
            record.setPackageWeight(ObjectUtil.isNotNull(sortingDTO.getPackageWeight()) ?
                    sortingDTO.getPackageWeight() : BigDecimal.ZERO);
            record.setPackageVolume(calculateVolume(sortingDTO.getPackageLength(),
                    sortingDTO.getPackageWidth(),
                    sortingDTO.getPackageHeight()));
            record.setMachineNumber(sortingDTO.getMachineCode());
            record.setPackageLength(sortingDTO.getPackageLength());
            record.setPackageWidth(sortingDTO.getPackageWidth());
            record.setPackageHeight(sortingDTO.getPackageHeight());

            // 设置分拣状态和条件信息
            if (context.isSuccess) {
                record.setSortingStatus(1);
                setSortingConditionInfo(record, context);
            } else {
                record.setSortingStatus(0);
                setSortingConditionInfo(record, context);
                record.setFailureReason(sortingVO.getMsg());
            }
            // 保存记录
            remoteTmsService.save(record);
            if(logger.isInfoEnabled()){
                logger.info("分拣记录保存成功，单号: {}", context.orderNo);
            }
            // 订单保存复合体积和重量
            remoteTmsService.addReviewVolumeAndWeight(record.getPackageVolume(), record.getPackageWeight(), context.orderNo);
        } catch (Exception e) {
            logger.error("异步保存分拣记录失败，单号: {}", context.orderNo, e);
        }
    }

    /**
     * 验证单号格式是否有效
     */
    private boolean isValidOrderNumber(String orderNumber) {
        return SUPPORTED_ORDER_PREFIXES.stream()
                .anyMatch(orderNumber::startsWith);
    }
    /**
     * 获取正向派送单
     * @param sortingDto
     * @return
     */
    private List<String> getDeliveryOrders(SortingDTO sortingDto) {
        if (CollectionUtil.isEmpty(sortingDto.getOrderNumber())) {
            throw new CustomBusinessException("单号列表不能为空");
        }
        List<String> validOrderNumbers = sortingDto
                .getOrderNumber()
                .stream()
                .filter(this::isValidOrderNumber)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(validOrderNumbers)) {
            throw new CustomBusinessException("未找到可处理的单号，支持的面单单号格式: N开头、NR开头、PUD开头、U9999开头、GV开头、JY开头");
        }
        List<String> validDeliveryOrders = validOrderNumbers
                .stream()
                .filter(this::isDeliveryOrder)
                .collect(Collectors.toList());

        // 替换列表中的元素数据
        for (int i = 0; i < validDeliveryOrders.size(); i++) {
            String validDeliveryOrder = validDeliveryOrders.get(i);
            boolean isMatched = SUPPORTED_PACKAGE_PREFIXES.stream().anyMatch(validDeliveryOrder::startsWith);
            if (isMatched) {
                TmsCustomerOrderEntity deliveryOrder = remoteTmsService.getCustomerOrderByOrderNo(validDeliveryOrder);
                if(null != deliveryOrder){
                    validDeliveryOrders.set(i, deliveryOrder.getEntrustedOrderNumber());
                }
            }
        }
        return validDeliveryOrders;
    }

    private boolean isDeliveryOrder(String orderNo) {
        return (orderNo.startsWith("N") &&
                orderNo.length() > DELIVERY_ORDER_CHAR_INDEX &&
                orderNo.charAt(DELIVERY_ORDER_CHAR_INDEX) == DELIVERY_ORDER_CHAR) ||
                (SUPPORTED_PACKAGE_PREFIXES.stream().anyMatch(orderNo::startsWith));
    }
    /**
     * 验证并获取有效的单号
     */
    private String validateAndGetOrderNo(SortingDTO sortingDTO) {
        if (CollectionUtil.isEmpty(sortingDTO.getOrderNumber())) {
            throw new CustomBusinessException("单号列表不能为空");
        }

        List<String> validOrderNumbers = sortingDTO.getOrderNumber().stream()
                .filter(this::isValidOrderNumber)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(validOrderNumbers)) {
            throw new CustomBusinessException("未找到可处理的单号，支持的面单单号格式: N开头、NR开头、PUD开头、U9999开头、GV开头、JY开头");
        }
        // TODO 目前分拣及窗格匹配只能处理一个
        return validOrderNumbers.get(0);
    }

    /**
     * 验证并获取启用的分拣模板
     */
    private TmsSortingTemplateDto validateAndGetSortingTemplate() {
        TmsSortingTemplateDto template = remoteTmsService.getEnableSortingTemplate();
        if (template == null || ObjectUtil.isNull(template.getId())) {
            throw new CustomBusinessException("未找到启用的分拣模版");
        }
        return template;
    }
    /**
     * 验证业务类型兼容性
     */
    private void validateBusinessTypeCompatibility(TmsSortingTemplateDto template, String orderNo) {
        boolean isDeliveryOrder = (orderNo.startsWith("N") &&
                orderNo.length() > DELIVERY_ORDER_CHAR_INDEX &&
                orderNo.charAt(DELIVERY_ORDER_CHAR_INDEX) == DELIVERY_ORDER_CHAR) || (
                SUPPORTED_PACKAGE_PREFIXES.stream().anyMatch(orderNo::startsWith));

        boolean isReturnOrder = orderNo.startsWith("NR") || orderNo.startsWith("PUD");

        // 退件分拣模版不支持派送单分拣
        if (template.getBusinessType().equals(BusinessType.COLLECTION.getCode()) && isDeliveryOrder) {
            throw new CustomBusinessException("当前启用的是退件分拣模版，不支持派送单分拣");
        }

        // 正向派送分拣模版不支持揽收退件单分拣
        if (template.getBusinessType().equals(BusinessType.DELIVERY.getCode()) && isReturnOrder) {
            throw new CustomBusinessException("当前启用的是正向派送分拣模版，不支持揽收退件单分拣");
        }
    }
    /**
     * 验证授权秘钥
     */
    private void validateAuthorization(SortingDTO sortingDto) {
        if (!AUTHORIZATION_KEY.equals(sortingDto.getKey())) {
            logger.error("[分拣设备][外部分拣接口]授权秘钥错误");
            throw new CustomBusinessException("授权秘钥错误");
        }
    }
    /**
     * 核心分拣处理逻辑
     */
    private int processSorting(SortingContext context) {
        String orderNo = context.orderNo;
        if (orderNo.startsWith("NR")) {
            return processReturnOrder(context);
        } else if (isDeliveryOrder(orderNo)) {
            return processDeliveryOrder(context);
        } else if (orderNo.startsWith("PUD")) {
            return processPudOrder(context);
        } else {
            throw new CustomBusinessException("不支持的单号格式: " + orderNo);
        }
    }
    /**
     * 处理退件揽收订单
     */
    private int processReturnOrder(SortingContext context) {
        SheinCodeEntity returnOrder = orderService.getOrderByOrderNo(context.orderNo);
        if (ObjectUtil.isNull(returnOrder)) {
            throw new CustomBusinessException("未找到该单号: " + context.orderNo);
        }
        context.returnOrder = returnOrder;
        determineGridId(context);
        ArrayList<Long> returnGridList = context.returnGridList;
        GlobalReturnCircularQueue returnQueue = context.returnQueue;
        if (CollUtil.isEmpty(returnGridList)) {
            throw new CustomBusinessException("未找到匹配的格口");
        }
        returnQueue.addAll(returnGridList);
        // 执行异步同步订单信息
        orderService.returnScanAsync(context.orderNo, 2, "", true);
        return returnQueue.next().intValue();
    }
    /**
     * 处理正向派送订单
     */
    private int processDeliveryOrder(SortingContext context) {
        // 跟踪单号或者客户单号查找
        TmsCustomerOrderEntity deliveryOrder = remoteTmsService.getCustomerOrderByOrderNo(context.orderNo);
        if (ObjectUtil.isNull(deliveryOrder)) {
            throw new CustomBusinessException("未找到该单号: " + context.orderNo);
        }
        // 处理面单和单号映射关系
        context.billOrderNo = context.orderNo;
        context.orderNo = deliveryOrder.getEntrustedOrderNumber();
        context.deliveryOrder = deliveryOrder;
        determineGridId(context);
        ArrayList<Long> deliveryGridList = context.deliveryGridList;
        GlobalDeliveryCircularQueue deliveryQueue = context.deliveryQueue;
        if (CollUtil.isEmpty(deliveryGridList)) {
            throw new CustomBusinessException("未找到匹配的格口");
        }
        // 添加到派送队列
        deliveryQueue.addAll(deliveryGridList);
        return deliveryQueue.next().intValue();
    }
    /**
     * 处理PUD订单
     */
    private int processPudOrder(SortingContext context) {
        List<Long> gridIdList = determineGridIdList(context);

        if (CollectionUtil.isEmpty(gridIdList)) {
            throw new CustomBusinessException("没有匹配的相应格口");
        }

        // 执行异步同步订单信息
        orderService.returnScanAsync(context.orderNo, 2, "", true);

        // 随机选择一个格口
        int randomIndex = ThreadLocalRandom.current().nextInt(gridIdList.size());
        return gridIdList.get(randomIndex).intValue();
    }
    /**
     * 确定格口ID列表（用于PUD等需要随机选择的场景）
     */
    private List<Long> determineGridIdList(SortingContext context) {
        List<Long> gridIdList = new ArrayList<>();
        SortingRuleType ruleType = SortingRuleType.lookup(context.sortingRuleType);

        if (ruleType == null) {
            throw new CustomBusinessException("不支持的分拣规则类型: " + context.sortingRuleType);
        }

        switch (ruleType) {
            case MERCHANT:
                // PUD使用固定商家ID
                Integer authId = PUD_MERCHANT_ID;
                List<TmsSortingMerchantDto> merchantDtoList = context.template.getTmsSortingMerchantDtoList();

                if (CollectionUtil.isNotEmpty(merchantDtoList)) {
                    for (TmsSortingMerchantDto merchantDto : merchantDtoList) {
                        if (merchantDto.getMerchantCodeList().contains(authId.longValue())) {
                            gridIdList.add(merchantDto.getGridId());
                        }
                    }
                }
                break;
            case ROUTE_NUMBER:
                throw new CustomBusinessException("退件揽收PUD不支持按照路线号分拣");
            case CONDITION:
                // 条件分拣暂不支持PUD随机选择
                throw new CustomBusinessException("PUD暂不支持条件分拣");
            default:
                throw new CustomBusinessException("不支持的分拣规则类型: " + ruleType.getValue());
        }

        return gridIdList;
    }
    /**
     * 按商家确定格口ID
     */
    private void determineGridIdByMerchant(SortingContext context) {
        if (isDeliveryOrder(context.orderNo)) {
            throw new CustomBusinessException("正向派送不支持按照商家分拣");
        }

        Integer authId = context.returnOrder != null ? context.returnOrder.getAuthId() : PUD_MERCHANT_ID;
        List<TmsSortingMerchantDto> merchantDtoList = context.template.getTmsSortingMerchantDtoList();

        if (CollectionUtil.isNotEmpty(merchantDtoList)) {
            for (TmsSortingMerchantDto merchantDto : merchantDtoList) {
                if (merchantDto.getMerchantCodeList().contains(authId.longValue())) {
                    Long gridId = merchantDto.getGridId();
                    context.returnGridList.add(gridId);
                }
            }
        }
    }
    /**
     * 按路线号确定格口ID
     */
    private void determineGridIdByRouteNumber(SortingContext context) {
        if (context.orderNo.startsWith("NR") || context.orderNo.startsWith("PUD")) {
            throw new CustomBusinessException("退件揽收不支持按照路线号分拣");
        }

        List<TmsSortingRouteNumberDto> routeNumberDtoList = context.template.getTmsSortingRouteNumberDtoList();
        if (CollectionUtil.isNotEmpty(routeNumberDtoList)) {
            for (TmsSortingRouteNumberDto routeNumberDto : routeNumberDtoList) {
                if (routeNumberDto.getRouteNumberList().contains(context.deliveryOrder.getRouteNumber())) {
                    Long gridId = routeNumberDto.getGridId();
                    context.deliveryGridList.add(gridId);
                }
            }
        }
    }
    /**
     * 按条件确定格口ID
     */
    private void determineGridIdByCondition(SortingContext context) {
        Order order = buildOrderForCondition(context);

        List<TmsSortingConditionDto> conditionDtoList = context.template.getTmsSortingConditionDtoList();
        if (CollectionUtil.isNotEmpty(conditionDtoList)) {
            for (TmsSortingConditionDto conditionDto : conditionDtoList) {
                ConditionGroup conditionGroup = conditionDto.getConditionGroup();
                String jsonString = JSON.toJSONString(conditionGroup);
                JSONObject jsonObject = JSON.parseObject(jsonString);
                String spel = BuildSpElUtil.buildSpEL(jsonObject);
                boolean satisfied = BuildSpElUtil.evaluateSpEL(spel, order);
                if (satisfied) {
                   conditionDto.getGridId();
                }
            }
        }
    }
    /**
     * 构建用于条件评估的订单对象
     */
    private Order buildOrderForCondition(SortingContext context) {
        Order order = new Order();
        if (context.returnOrder != null) {
            // 退件订单
            order.setWeight(context.returnOrder.getPackageWeight());
            order.setCity(context.returnOrder.getConsigneeUnifiedAddress());
            order.setVolume(calculateVolume(context.returnOrder.getPackageLength(),
                    context.returnOrder.getPackageWidth(),
                    context.returnOrder.getPackageHeight()));
        } else if (context.deliveryOrder != null) {
            // 派送订单
            order.setWeight(context.deliveryOrder.getTotalWeight());
            order.setCity(context.deliveryOrder.getDestination());
            order.setVolume(context.deliveryOrder.getTotalVolume());
        }

        return order;
    }
    /**
     * 计算包裹体积
     */
    private BigDecimal calculateVolume(BigDecimal length, BigDecimal width, BigDecimal height) {
        if (ObjectUtil.isNull(length) || ObjectUtil.isNull(width) || ObjectUtil.isNull(height)) {
            return BigDecimal.ZERO;
        }
        return length.multiply(width).multiply(height).divide(BigDecimal.valueOf(1000000));
    }
    /**
     * 处理业务异常
     */
    private void handleBusinessException(SortingVO sortingVo, SortingContext context, CustomBusinessException e) {
        context.isSuccess = false;
        context.failMsg = e.getMessage();
        sortingVo.setCode(400);
        sortingVo.setMsg(context.failMsg);
        logger.error("分拣业务异常，单号: {}, 错误: {}", context.orderNo, context.failMsg);
        throw e;
    }
    /**
     * 处理意外异常
     */
    private void handleUnexpectedException(SortingVO sortingVo, SortingContext context, Exception e) {
        context.isSuccess = false;
        context.failMsg = "系统内部错误: " + e.getMessage();
        sortingVo.setCode(500);
        sortingVo.setMsg(context.failMsg);
        logger.error("分拣系统异常，单号: {}, 错误: {}", context.orderNo, context.failMsg, e);
        throw new RuntimeException(context.failMsg, e);
    }

    /**
     * 设置分拣条件信息
     */
    private void setSortingConditionInfo(TmsSortingRecordEntity record, SortingContext context) {
        SortingRuleType ruleType = SortingRuleType.lookup(context.sortingRuleType);
        if (ruleType == null) {
            return;
        }
        switch (ruleType) {
            case MERCHANT:
                if (context.orderNo.startsWith("PUD")) {
                    record.setMerchant(PUD_MERCHANT_ID);
                } else if (context.returnOrder != null) {
                    record.setMerchant(context.returnOrder.getAuthId());
                    record.setCity(context.returnOrder.getConsigneeUnifiedAddress());
                }
                break;
            case ROUTE_NUMBER:
                if (context.deliveryOrder != null) {
                    record.setRouteNumber(context.deliveryOrder.getRouteNumber());
                    record.setCity(context.deliveryOrder.getDestination());
                }
                break;
            case CONDITION:
                // 条件分拣的记录信息可以在后续扩展
                break;
            default:
                if(logger.isWarnEnabled()){
                    logger.warn("未知的分拣规则类型: {}", ruleType.getValue());
                }

        }
    }
    /**
     * 确定格口ID（单个）
     */
    private void determineGridId(SortingContext context) {
        SortingRuleType ruleType = SortingRuleType.lookup(context.sortingRuleType);
        if (ruleType == null) {
            throw new CustomBusinessException("不支持的分拣规则类型: " + context.sortingRuleType);
        }
        switch (ruleType) {
            case MERCHANT:
                    determineGridIdByMerchant(context);
                    break;
            case ROUTE_NUMBER:
                    determineGridIdByRouteNumber(context);
                    break;
            case CONDITION:
                    determineGridIdByCondition(context);
                    break;
            default:
                throw new CustomBusinessException("不支持的分拣规则类型: " + ruleType.getValue());
        }
    }
    /**
     * 半托管单号匹配处理 HalfTrusteeship
     */
    private void processHalfOrderNumbers(SortingContext context,List<String> orderNumber){
        // 优先级最高
        for (String no : orderNumber) {
            Matcher matcher = HALF_TRUSTEESHIP_PATTERN.matcher(no.trim());
            if (matcher.matches()) {
                context.orderNo = matcher.group(1);
            }
        }
        if(StrUtil.isNotBlank(context.orderNo)){
            return;
        }
        // 次优先级
        for (String no : orderNumber) {
            boolean isMatched = HALF_TRUSTEESHIP_ORDER_PREFIXES.stream().anyMatch(no::startsWith);
            if(isMatched){
                context.orderNo = no;
            }
        }
        if(StrUtil.isNotBlank(context.orderNo)){
            return;
        }
        context.orderNo = orderNumber.get(0);
    }

    /**
     * 半托管单号是否为系统单号标记
     * @param context
     */
    private void markHalfMainFlag(SortingContext context){
        String orderNo = context.orderNo;
        boolean isMatched = HALF_TRUSTEESHIP_ORDER_PREFIXES.stream().anyMatch(orderNo::startsWith);
        if(isMatched){
            context.mainFlag = 1;
        }
    }


    /**
     * 分拣上下文，用于传递处理过程中的数据
     */
    private static class SortingContext {
        String orderNo;
        String billOrderNo;
        // 是否总部单号
        Integer mainFlag = 0;
        TmsSortingTemplateDto template;
        Integer sortingRuleType;
        SheinCodeEntity returnOrder;
        TmsCustomerOrderEntity deliveryOrder;
        GlobalReturnCircularQueue returnQueue;
        GlobalDeliveryCircularQueue deliveryQueue;
        ArrayList<Long> returnGridList;
        ArrayList<Long> deliveryGridList;
        boolean isSuccess;
        String failMsg;
    }
}
