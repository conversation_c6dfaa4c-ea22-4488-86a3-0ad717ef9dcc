package com.jygjexp.jynx.basic.back.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 
 * @discription 数值字段范围注解
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface NumberRange {
	String message() default "";
	double min() default Double.MIN_VALUE;
	double max() default Double.MAX_VALUE;

}
