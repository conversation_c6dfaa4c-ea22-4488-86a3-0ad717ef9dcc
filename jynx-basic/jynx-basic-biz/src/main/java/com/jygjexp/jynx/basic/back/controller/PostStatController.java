package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.model.bo.PostStatPageBo;
import com.jygjexp.jynx.basic.back.model.vo.excel.PostStatExcelVo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.PostStatEntity;
import com.jygjexp.jynx.basic.back.service.PostStatService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 驿站统计
 *
 * <AUTHOR>
 * @date 2024-11-11 21:18:56
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/postStat")
@Tag(description = "postStat", name = "驿站统计管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PostStatController {

    private final PostStatService postStatService;

    /**
     * 分页查询
     *
     * @param page     分页对象
     * @param postStat 驿站统计
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_PostStat_view')")
    public R getPostStatPage(@ParameterObject Page page, @ParameterObject PostStatPageBo postStat) {
        return R.ok(postStatService.getPage(page, postStat));
    }


    /**
     * 通过id查询驿站统计
     *
     * @param statId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{statId}")
    @PreAuthorize("@pms.hasPermission('back_PostStat_view')")
    public R getById(@PathVariable("statId") Integer statId) {
        return R.ok(postStatService.getById(statId));
    }

    /**
     * 新增驿站统计
     *
     * @param postStat 驿站统计
     * @return R
     */
    @Operation(summary = "新增驿站统计", description = "新增驿站统计")
    @SysLog("新增驿站统计")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_PostStat_add')")
    public R save(@RequestBody PostStatEntity postStat) {
        return R.ok(postStatService.save(postStat));
    }

    /**
     * 修改驿站统计
     *
     * @param postStat 驿站统计
     * @return R
     */
    @Operation(summary = "修改驿站统计", description = "修改驿站统计")
    @SysLog("修改驿站统计")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_PostStat_edit')")
    public R updateById(@RequestBody PostStatEntity postStat) {
        return R.ok(postStatService.updateById(postStat));
    }

    /**
     * 通过id删除驿站统计
     *
     * @param ids statId列表
     * @return R
     */
    @Operation(summary = "通过id删除驿站统计", description = "通过id删除驿站统计")
    @SysLog("通过id删除驿站统计")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_PostStat_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(postStatService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 导出excel 表格
     *
     * @param postStat 查询条件
     * @param ids      导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_PostStat_export')")
    public List<PostStatExcelVo> export(PostStatPageBo postStat, Integer[] ids) {
        return postStatService.getExcel(postStat, ids);
    }

}