package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.PhotoEntity;
import com.jygjexp.jynx.basic.back.service.ZtPhotoService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 驿站图库
 *
 * <AUTHOR>
 * @date 2024-09-30 18:22:56
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/ztPhoto" )
@Tag(description = "ztPhoto" , name = "驿站图库管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PhotoController {

    private final ZtPhotoService ztPhotoService;


    /**
     * 分页查询
     * @param page 分页对象
     * @param ztPhoto 驿站图库
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('jynx_ZtPhoto_view')" )
    public R getZtPhotoPage(@ParameterObject Page page, @ParameterObject PhotoEntity ztPhoto) {
        LambdaQueryWrapper<PhotoEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(ztPhotoService.page(page, wrapper));
    }


    /**
     * 通过id查询驿站图库
     * @param photoId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{photoId}" )
    @PreAuthorize("@pms.hasPermission('jynx_ZtPhoto_view')" )
    public R getById(@PathVariable("photoId" ) Integer photoId) {
        return R.ok(ztPhotoService.getById(photoId));
    }

    /**
     * 新增驿站图库
     * @param ztPhoto 驿站图库
     * @return R
     */
    @Operation(summary = "新增驿站图库" , description = "新增驿站图库" )
    @SysLog("新增驿站图库" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('jynx_ZtPhoto_add')" )
    public R save(@RequestBody PhotoEntity ztPhoto) {
        return R.ok(ztPhotoService.save(ztPhoto));
    }

    /**
     * 修改驿站图库
     * @param ztPhoto 驿站图库
     * @return R
     */
    @Operation(summary = "修改驿站图库" , description = "修改驿站图库" )
    @SysLog("修改驿站图库" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('jynx_ZtPhoto_edit')" )
    public R updateById(@RequestBody PhotoEntity ztPhoto) {
        return R.ok(ztPhotoService.updateById(ztPhoto));
    }

    /**
     * 通过id删除驿站图库
     * @param ids photoId列表
     * @return R
     */
    @Operation(summary = "通过id删除驿站图库" , description = "通过id删除驿站图库" )
    @SysLog("通过id删除驿站图库" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('jynx_ZtPhoto_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(ztPhotoService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param ztPhoto 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('jynx_ZtPhoto_export')" )
    public List<PhotoEntity> export(PhotoEntity ztPhoto, Integer[] ids) {
        return ztPhotoService.list(Wrappers.lambdaQuery(ztPhoto).in(ArrayUtil.isNotEmpty(ids), PhotoEntity::getPhotoId, ids));
    }
}