package com.jygjexp.jynx.basic.back.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 自定义异常类
 * <AUTHOR>
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomBusinessException extends RuntimeException {

    private int errorCode;

    private String message;

    public CustomBusinessException(int errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }
    public CustomBusinessException(String message) {
        this.errorCode = 400;
        this.message = message;
    }

}
