package com.jygjexp.jynx.basic.back.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.ActivityEntity;
import com.jygjexp.jynx.basic.back.service.ActivityService;
import com.jygjexp.jynx.common.core.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;


@RestController
@RequiredArgsConstructor
@RequestMapping("/activity")
@Tag(description = "activity", name = "驿站活动管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ActivityController {


    private final ActivityService activityService;

    // 新增
    @PostMapping("/add")
    @Operation(summary = "新增", description = "新增")
    @PreAuthorize("@pms.hasPermission('back_activity_add')")
    public boolean addActivity(@RequestBody ActivityEntity activityEntity) {
        activityEntity.setCreateDate(LocalDateTime.now());
        return activityService.save(activityEntity);
    }

    // 编辑
    @PutMapping("/update")
    @PreAuthorize("@pms.hasPermission('back_activity_edit')")
    @Operation(summary = "更新", description = "更新")
    public boolean updateActivity(@RequestBody ActivityEntity activityEntity) {
        return activityService.updateById(activityEntity);
    }

    // 删除
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除", description = "删除")
    @PreAuthorize("@pms.hasPermission('back_activity_del')")
    public boolean deleteActivity(@PathVariable Integer id) {
        return activityService.removeById(id);
    }


    // 分页查询
    @GetMapping("/list")
    @Operation(summary = "分页查询", description = "分页查询")
    @PreAuthorize("@pms.hasPermission('back_activity_view')")
    public R listActivities(@ParameterObject Page page, @ParameterObject ActivityEntity activity) {
        return R.ok(activityService.getPage(page, activity));
    }


    //分配驿站
    @PutMapping("/distributePost")
    @PreAuthorize("@pms.hasPermission('back_activity_allocation')")
    @Operation(summary = "分配驿站", description = "分配驿站")
    public R updateActivity(@RequestParam Integer id, String distributeIds, String cancelIds) {
        boolean flag = activityService.distributePost(id, distributeIds, cancelIds);
        return flag ? R.ok("分配驿站成功") : R.failed("分配驿站失败");
    }


}

