package com.jygjexp.jynx.basic.back.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.basic.back.constants.AuthConstants;
import com.jygjexp.jynx.basic.back.constants.PrintConstants;
import com.jygjexp.jynx.basic.back.constants.TkzjConstants;
import com.jygjexp.jynx.basic.back.entity.PostEntity;
import com.jygjexp.jynx.basic.back.entity.PrintDeviceEntity;
import com.jygjexp.jynx.basic.back.entity.SheinCodeEntity;
import com.jygjexp.jynx.basic.back.entity.SubOrderEntity;
import com.jygjexp.jynx.basic.back.mapper.PrintDeviceMapper;
import com.jygjexp.jynx.basic.back.model.vo.PrintInfoVo;
import com.jygjexp.jynx.basic.back.service.*;
import com.jygjexp.jynx.basic.back.tools.*;
import com.jygjexp.jynx.basic.response.LocalizedR;
import com.jygjexp.jynx.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 云打印对接
 */
@Service
@Slf4j
public class PrintServiceImpl implements PrintService {


    @Value("${printer.country:CN}")
    private String country;
    @Autowired
    private OrderService orderService;
    @Autowired
    private PostPrintRecordService postPrintRecordService;
    @Autowired
    private PrintDeviceMapper printDeviceMapper;

    private static final String TEST_URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/Test%20successful.pdf";


    //执行打印
    @Override
    public R printLabel(String deviceId, String labelUrl, String orderNo, Integer postId) {
        if (StringUtils.isBlank(deviceId)){
            //通过驿站ID查询打印机ID
            PrintDeviceEntity printer =getPrintByPostId(postId);
            if (printer == null){
                return LocalizedR.failed("return.printer.not.exist");
            }
            deviceId = printer.getDeviceId();
        }

        String tokenFromCache = getTokenFromCache();
        if (StringUtils.isBlank(tokenFromCache)){
            return R.failed("get token failed");
        }
        try {
            log.info("开始处理打印...");
            //没有面单就不打印
            SheinCodeEntity order = orderService.getOrderByOrderNo(orderNo);
            if (order!= null && order.getAuthId()== AuthConstants.AUTH_YSP){
                labelUrl="http://api.neighbourexpress.com/zt/api/return/printLabel?id=" + URLEncoder.encode(order.getLabelCode(), "UTF-8");
                log.info("YSP面单处理打印开始..."+labelUrl);
                labelUrl= AliYunOSS.sendToOssFromByteArray(OrderTools.getBytesFromUrl(labelUrl), "print", order.getSheinCode() + ".pdf").replace("-internal", "");
                log.info("YSP面单处理打印结束..."+labelUrl);
            }
            if ("createTest".equals(orderNo)) {
                labelUrl = TEST_URL;
            } else if (StringUtils.isBlank(labelUrl)) {
                if (order == null || StringUtils.isBlank(order.getLabelPath())) {
                    return LocalizedR.failed("return.order.not.exist", orderNo);
                }
                labelUrl = order.getLabelPath();
            }

            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                    .addFormDataPart("app_id", String.valueOf(PrintConstants.APP_ID))
                    .addFormDataPart("access_token", tokenFromCache)
                    .addFormDataPart("device_ids", deviceId)
                    .addFormDataPart("copies", "1")
                    .addFormDataPart("file_type", "1")
                    .addFormDataPart("cus_orderid", postId + "-" + System.currentTimeMillis())
                    .addFormDataPart("bill_content", labelUrl)
                    .addFormDataPart("paper_width", "100")
                    .addFormDataPart("paper_height", "150")
                    .addFormDataPart("paper_type", "2")
                    .addFormDataPart("time_out", "1500")
                    .addFormDataPart("reverse", "1")
                    .addFormDataPart("walk_paper", "1")
                    .build();
            Request request = new Request.Builder()
                    .url("http://global.jolimark.com/mcp/v3/sys/PrintFileByUrl")
                    .method("POST", body)
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Accept", "*/*")
                    .addHeader("Host", "global.jolimark.com")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type", "multipart/form-data")
                    .build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                postPrintRecordService.printCount(deviceId,order==null?"TEST_ORDER":order.getSheinCode(),labelUrl, postId, tokenFromCache+";"+country);
                return R.ok(tokenFromCache);
            }
        } catch (Exception e) {
            return R.failed("print failed");
        }
        return R.failed("print failed");
    }


    //根据驿站ID查询打印设备
    public PrintDeviceEntity getPrintByPostId(Integer postId) {
        LambdaQueryWrapper<PrintDeviceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(PrintDeviceEntity::getDeviceId)
                .eq(PrintDeviceEntity::getPostId, postId)
                .last("LIMIT 1");
        return printDeviceMapper.selectOne(wrapper);
    }


    //获取打印机状态
    @Override
    public R getPrintStatus(String deviceId) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        Request request = new Request.Builder()
                .url("https://wiot.iprtapp.com/sw/api/device/getDevice/" + deviceId)
                .method("GET", null)
                .addHeader("Accept", "*/*")
                .addHeader("Host", "wiot.iprtapp.com")
                .addHeader("Connection", "keep-alive")
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();

                // 将响应字符串转换为 JSONObject
                JSONObject jsonResponse = new JSONObject(responseBody);

                // 获取 data 对象
                JSONObject data = jsonResponse.getJSONObject("data");

                // 提取 status 和 state
                int status = data.getInt("status");
                String state = data.getString("state");
                //更新设备状态
                updateDeviceStatus(deviceId, status, state);

                PrintInfoVo printInfoVo = new PrintInfoVo();
                printInfoVo.setDeviceId(deviceId);
                printInfoVo.setStatus(status);
                printInfoVo.setState(state);
                return R.ok(printInfoVo);
            } else {
                return R.failed();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }

    //根据设备id更新设备状态
    public void updateDeviceStatus(String deviceId, int status, String state) {
        LambdaUpdateWrapper<PrintDeviceEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PrintDeviceEntity::getDeviceId, deviceId)
                .set(PrintDeviceEntity::getStatus, status)
                .set(PrintDeviceEntity::getState, state);
        printDeviceMapper.update(wrapper);
    }

    //获取待打印队列报文(还不知道用不用，暂时这样)
    @Override
    public String getWaitingMsg(String deviceId) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        Request request = new Request.Builder()
                .url("https://wiot.iprtapp.com/sw/api/message/waitingMsg/" + deviceId)
                .method("GET", null)
                .addHeader("Accept", "*/*")
                .addHeader("Host", "wiot.iprtapp.com")
                .addHeader("Connection", "keep-alive")
                .build();
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    //清空打印队列(还不知道用不用，暂时这样)
    @Override
    public R printClear(String deviceId) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("devid", deviceId);
            RequestBody body = RequestBody.create(mediaType, jsonObject.toString());
            Request request = new Request.Builder()
                    .url("https://wiot.iprtapp.com/sw/api/message/clear")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "*/*")
                    .addHeader("Host", "wiot.iprtapp.com")
                    .addHeader("Connection", "keep-alive")
                    .build();
            Response response = client.newCall(request).execute();
            return R.ok(response.message());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }


    //获取Token
    public String getNewToken() {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        long timestamp = Instant.now().getEpochSecond();
        // 计算签名
        String md5 = OrderTools.getMD5("app_id=" + PrintConstants.APP_ID + "&sign_type=MD5&time_stamp=" + timestamp + "&key=" + PrintConstants.APP_SECRET).toUpperCase();
        String outUrl = "http://global.jolimark.com/mcp/v3/sys/GetAccessToken";
        // 拼接参数到URL
        HttpUrl url = HttpUrl.parse(outUrl).newBuilder()
                .addQueryParameter("app_id", String.valueOf(PrintConstants.APP_ID))
                .addQueryParameter("time_stamp", String.valueOf(timestamp))
                .addQueryParameter("sign", md5)
                .addQueryParameter("sign_type", "MD5")
                .build();

        Request request = new Request.Builder()
                .url(url)
                .get()
                .addHeader("Accept", "*/*")
                .addHeader("Connection", "keep-alive")
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseJson = response.body().string();
                ObjectMapper mapper = new ObjectMapper();
                JsonNode rootNode = mapper.readTree(responseJson);
                JsonNode accessTokenNode = rootNode.path("return_data").path("access_token");
                return accessTokenNode.asText();
            }
        }catch (Exception e){
            e.printStackTrace();
            return PrintConstants.PRINT_TOKEN;
        }
        return PrintConstants.PRINT_TOKEN;
    }



    //从缓存中获取token(本地环境不允许获取)
    public String getTokenFromCache() {
        if ("CN".equals(country)){
            return PrintConstants.PRINT_TOKEN;
        }
        // 获取缓存中存储的值
        LocalCacheMap cache = LocalCacheMap.getInstance();
        try {
            Object data = cache.get("print_token");
            if (ObjectUtil.isNotNull(data) && StringUtils.isNotBlank(data.toString())) {
                String[] strings = data.toString().split(";");
                String time = strings[0];
                String token = strings[1];
               if (OrderTools.isValidToken(Long.valueOf(time))){
                   System.out.println("token有效"+token);
                   return token;
               }
            }
        }catch (Exception e){
            e.printStackTrace();
            //保证每次都能获取到，不影响打印
            return getNewToken();
        }
        String newToken = getNewToken();
        System.out.println("获取新token"+newToken);
        cache.set("print_token",java.time.Instant.now().getEpochSecond()+";"+newToken);
        return newToken;
    }


    //绑定打印机
    @Override
    public R binding(String deviceId) {
        String tokenFromCache = getTokenFromCache();
        if (StringUtils.isBlank(tokenFromCache)){
            return R.failed("get token failed");
        }
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("app_id", String.valueOf(PrintConstants.APP_ID))
                .addFormDataPart("access_token", tokenFromCache)
                .addFormDataPart("device_codes", deviceId)
                //参数1-仅国外使用
                .addFormDataPart("printer_location", "1")
                .build();
        Request request = new Request.Builder()
                .url("http://global.jolimark.com/mcp/v3/sys/BindPrinter")
                .method("POST", body)
                .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .addHeader("Accept", "*/*")
                .addHeader("Host", "global.jolimark.com")
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Type", "multipart/form-data; boundary=--------------------------467413057243532591384036")
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                if (responseBody.contains("OK")) {
                    return R.ok();
                } else if (responseBody.contains("校验码")) {
                    return LocalizedR.failed("printer.check.code.error", Optional.ofNullable(null));
                } else if (responseBody.contains("不存在")) {
                    return LocalizedR.failed("printer.not.exist", Optional.ofNullable(null));

                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.failed("error");
    }

    @Override
    public Boolean Unbinding(String deviceId) {
        String tokenFromCache = getTokenFromCache();
        if (StringUtils.isBlank(tokenFromCache)){
           return false;
        }
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("app_id", String.valueOf(PrintConstants.APP_ID))
                .addFormDataPart("access_token", tokenFromCache)
                .addFormDataPart("device_id", deviceId)
                .build();
        Request request = new Request.Builder()
                .url("https://global.jolimark.com/mcp/v3/sys/UnBindPrinter")
                .method("POST", body)
                .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .addHeader("Accept", "*/*")
                .addHeader("Host", "global.jolimark.com")
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Type", "multipart/form-data; boundary=--------------------------065583106761376721534757")
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                if (responseBody.contains("OK")) {
                    return true;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }


    //获取打印机详情
    @Override
    public String getDetail(String deviceId) {
        String tokenFromCache = getTokenFromCache();
        if (StringUtils.isBlank(tokenFromCache)){
            return "";
        }
        OkHttpClient client = new OkHttpClient();
        // 构造带查询参数的 URL
        HttpUrl url = HttpUrl.parse("http://global.jolimark.com/mcp/v3/sys/QueryPrinterInfo")
                .newBuilder()
                .addQueryParameter("app_id", String.valueOf(PrintConstants.APP_ID))
                .addQueryParameter("access_token", tokenFromCache)
                .addQueryParameter("device_id", deviceId)
                .build();

        Request request = new Request.Builder()
                .url(url)
                .get()
                .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .addHeader("Accept", "*/*")
                .addHeader("Host", "global.jolimark.com")
                .addHeader("Connection", "keep-alive")
                .build();

        Response response = null;
        String responseBody = "";
        try {
            response = client.newCall(request).execute();
            responseBody = response.body().string();
        } catch (Exception e) {
            return "";
        }
        return responseBody;
    }


    //判断token是否有效
    @Override
    public void isValidToken() {
        if ("CN".equals(country)){
            System.out.println("国内环境不需要校验");
        }else {
            String detail = getDetail("25030248AZZ");
            if (detail.contains("不合法")){
                LocalCacheMap cache = LocalCacheMap.getInstance();
                String newToken = getNewToken();
                cache.set("print_token",java.time.Instant.now().getEpochSecond()+";"+newToken);
                System.out.println("刷新token"+newToken);
            }
        }
    }


}
