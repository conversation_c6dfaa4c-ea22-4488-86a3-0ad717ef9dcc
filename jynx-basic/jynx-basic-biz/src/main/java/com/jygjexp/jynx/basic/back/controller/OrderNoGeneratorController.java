package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.OrderNoGeneratorEntity;
import com.jygjexp.jynx.basic.back.service.OrderNoGeneratorService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 单号生成器
 *
 * <AUTHOR>
 * @date 2024-10-09 21:42:11
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tkzjZtOrderNoGenerator" )
@Tag(description = "tkzjZtOrderNoGenerator" , name = "单号生成器管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class OrderNoGeneratorController {

    private final OrderNoGeneratorService OrderNoGeneratorService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tkzjZtOrderNoGenerator 单号生成器
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('back_tkzjZtOrderNoGenerator_view')" )
    public R getTkzjZtOrderNoGeneratorPage(@ParameterObject Page page, @ParameterObject OrderNoGeneratorEntity tkzjZtOrderNoGenerator) {
        LambdaQueryWrapper<OrderNoGeneratorEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(OrderNoGeneratorService.page(page, wrapper));
    }


    /**
     * 通过id查询单号生成器
     * @param generatorId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{generatorId}" )
    @PreAuthorize("@pms.hasPermission('back_ZtOrderNoGenerator_view')" )
    public R getById(@PathVariable("generatorId" ) Integer generatorId) {
        return R.ok(OrderNoGeneratorService.getById(generatorId));
    }

    /**
     * 新增单号生成器
     * @param ZtOrderNoGenerator 单号生成器
     * @return R
     */
    @Operation(summary = "新增单号生成器" , description = "新增单号生成器" )
    @SysLog("新增单号生成器" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_ZtOrderNoGenerator_add')" )
    public R save(@RequestBody OrderNoGeneratorEntity ZtOrderNoGenerator) {
        return R.ok(OrderNoGeneratorService.save(ZtOrderNoGenerator));
    }

    /**
     * 修改单号生成器
     * @param ZtOrderNoGenerator 单号生成器
     * @return R
     */
    @Operation(summary = "修改单号生成器" , description = "修改单号生成器" )
    @SysLog("修改单号生成器" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_ZtOrderNoGenerator_edit')" )
    public R updateById(@RequestBody OrderNoGeneratorEntity ZtOrderNoGenerator) {
        return R.ok(OrderNoGeneratorService.updateById(ZtOrderNoGenerator));
    }

    /**
     * 通过id删除单号生成器
     * @param ids generatorId列表
     * @return R
     */
    @Operation(summary = "通过id删除单号生成器" , description = "通过id删除单号生成器" )
    @SysLog("通过id删除单号生成器" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_tkzjZtOrderNoGenerator_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(OrderNoGeneratorService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param ZtOrderNoGenerator 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_ZtOrderNoGenerator_export')" )
    public List<OrderNoGeneratorEntity> export(OrderNoGeneratorEntity ZtOrderNoGenerator, Integer[] ids) {
        return OrderNoGeneratorService.list(Wrappers.lambdaQuery(ZtOrderNoGenerator).in(ArrayUtil.isNotEmpty(ids), OrderNoGeneratorEntity::getGeneratorId, ids));
    }
}