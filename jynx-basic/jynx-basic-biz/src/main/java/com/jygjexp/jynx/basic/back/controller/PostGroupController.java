package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.model.vo.excel.PostGroupExcelVo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.PostGroupEntity;
import com.jygjexp.jynx.basic.back.service.PostGroupService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 驿站组
 *
 * <AUTHOR>
 * @date 2024-11-07 14:15:16
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/postGroup")
@Tag(description = "postGroup", name = "驿站组管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PostGroupController {

    private final PostGroupService postGroupService;

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param postGroup 驿站组
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    //@PreAuthorize("@pms.hasPermission('back_PostGroup_view')")
    public R getPostGroupPage(@ParameterObject Page page, @ParameterObject PostGroupEntity postGroup) {
        return R.ok(postGroupService.getPage(page, postGroup));
    }


    /**
     * 通过id查询驿站组
     *
     * @param groupId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{groupId}")
    @PreAuthorize("@pms.hasPermission('back_PostGroup_view')")
    public R getById(@PathVariable("groupId") Integer groupId) {
        return R.ok(postGroupService.getDetialById(groupId));
    }

    /**
     * 新增驿站组
     *
     * @param postGroup 驿站组
     * @return R
     */
    @Operation(summary = "新增驿站组", description = "新增驿站组")
    @SysLog("新增驿站组")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_PostGroup_add')")
    public R save(@RequestBody PostGroupEntity postGroup) {
        return postGroupService.savePostGroup(postGroup);
    }

    /**
     * 修改驿站组
     *
     * @param postGroup 驿站组
     * @return R
     */
    @Operation(summary = "修改驿站组", description = "修改驿站组")
    @SysLog("修改驿站组")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_PostGroup_edit')")
    public R updateById(@RequestBody PostGroupEntity postGroup) {
        return R.ok(postGroupService.updateById(postGroup));
    }

    /**
     * 通过id删除驿站组
     *
     * @param ids groupId列表
     * @return R
     */
    @Operation(summary = "通过id删除驿站组", description = "通过id删除驿站组")
    @SysLog("通过id删除驿站组")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_PostGroup_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(postGroupService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param postGroup 查询条件
     * @param ids       导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_PostGroup_export')")
    public List<PostGroupExcelVo> export(PostGroupEntity postGroup, Integer[] ids) {
        return postGroupService.getExcel(postGroup, ids);
    }
}