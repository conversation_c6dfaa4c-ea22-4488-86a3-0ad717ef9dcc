package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.model.bo.ApiAuthPageBo;
import com.jygjexp.jynx.basic.back.model.vo.excel.PartnerExcelVo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.ApiAuthEntity;
import com.jygjexp.jynx.basic.back.service.ApiAuthService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * api认证
 *
 * <AUTHOR>
 * @date 2024-10-14 21:11:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/apiAuth")
@Tag(description = "apiAuth", name = "api认证管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ApiAuthController {

    private final ApiAuthService apiAuthService;

    /**
     * 分页查询
     *
     * @param page    分页对象
     * @param apiAuth api认证
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_ApiAuth_view')")
    public R getApiAuthPage(@ParameterObject Page page, @ParameterObject ApiAuthPageBo apiAuth) {

        return R.ok(apiAuthService.getPage(page, apiAuth));
    }


    /**
     * 通过id查询api认证
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('back_ApiAuth_view')")
    public R getById(@PathVariable("id") Integer id) {
        return R.ok(apiAuthService.getById(id));
    }

    /**
     * 新增api认证
     *
     * @param apiAuth api认证
     * @return R
     */
    @Operation(summary = "新增api认证", description = "新增api认证")
    @SysLog("新增api认证")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_ApiAuth_add')")
    public R save(@RequestBody ApiAuthEntity apiAuth) {
        apiAuth.setCreateDate(LocalDateTime.now());
        return R.ok(apiAuthService.save(apiAuth));
    }

    /**
     * 修改api认证
     *
     * @param apiAuth api认证
     * @return R
     */
    @Operation(summary = "修改api认证", description = "修改api认证")
    @SysLog("修改api认证")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_ApiAuth_edit')")
    public R updateById(@RequestBody ApiAuthEntity apiAuth) {
        return R.ok(apiAuthService.updateById(apiAuth));
    }

    /**
     * 通过id删除api认证
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除api认证", description = "通过id删除api认证")
    @SysLog("通过id删除api认证")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_ApiAuth_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(apiAuthService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 合作商报价导出
     *
     * @param apiAuth 查询条件
     * @param ids     导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export1")
    @PreAuthorize("@pms.hasPermission('back_ApiAuth_export')")
    public List<ApiAuthEntity> export1(ApiAuthEntity apiAuth, Integer[] ids) {
        return apiAuthService.list(Wrappers.lambdaQuery(apiAuth).in(ArrayUtil.isNotEmpty(ids), ApiAuthEntity::getId, ids));
    }


    /**
     * 合作商列表导出
     *
     * @param apiAuth 查询条件
     * @param ids     导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export2")
    @PreAuthorize("@pms.hasPermission('back_ApiAuth_export')")
    public List<PartnerExcelVo> export2(ApiAuthPageBo apiAuth, Integer[] ids) {
        return apiAuthService.getExcel(apiAuth, ids);
    }
}