package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.PostEntity;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.WarehouseEntity;
import com.jygjexp.jynx.basic.back.service.WarehouseService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 仓库
 *
 * <AUTHOR>
 * @date 2024-11-08 14:45:42
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/warehouse")
@Tag(description = "warehouse", name = "仓库管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class WarehouseController {

    private final WarehouseService warehouseService;

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param warehouse 仓库
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_Warehouse_view')")
    public R getWarehousePage(@ParameterObject Page page, @ParameterObject WarehouseEntity warehouse) {
        LambdaQueryWrapper<WarehouseEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(warehouse.getWarehouseName()), WarehouseEntity::getWarehouseName, warehouse.getWarehouseName())
                .like(StringUtils.isNotBlank(warehouse.getWarehouseCode()), WarehouseEntity::getWarehouseCode, warehouse.getWarehouseCode());
        return R.ok(warehouseService.page(page, wrapper));
    }


    /**
     * 通过id查询仓库
     *
     * @param warehouseId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{warehouseId}")
    @PreAuthorize("@pms.hasPermission('back_Warehouse_view')")
    public R getById(@PathVariable("warehouseId") Integer warehouseId) {
        return R.ok(warehouseService.getById(warehouseId));
    }

    /**
     * 新增仓库
     *
     * @param warehouse 仓库
     * @return R
     */
    @Operation(summary = "新增仓库", description = "新增仓库")
    @SysLog("新增仓库")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_Warehouse_add')")
    public R save(@RequestBody WarehouseEntity warehouse) {
        warehouse.setCreateDate(LocalDateTime.now());
        return R.ok(warehouseService.save(warehouse));
    }

    /**
     * 修改仓库
     *
     * @param warehouse 仓库
     * @return R
     */
    @Operation(summary = "修改仓库", description = "修改仓库")
    @SysLog("修改仓库")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_Warehouse_edit')")
    public R updateById(@RequestBody WarehouseEntity warehouse) {
        return R.ok(warehouseService.updateById(warehouse));
    }

    /**
     * 通过id删除仓库
     *
     * @param ids warehouseId列表
     * @return R
     */
    @Operation(summary = "通过id删除仓库", description = "通过id删除仓库")
    @SysLog("通过id删除仓库")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_Warehouse_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(warehouseService.removeBatchByIds(CollUtil.toList(ids)));
    }

    //获取当前账号仓库
    @GetMapping("/getWarehouse")
    @Operation(summary = "获取当前账号仓库", description = "获取当前账号仓库")
    @PreAuthorize("@pms.hasPermission('back_Warehouse_view')")
    public R getWarehouse() {
        return R.ok(warehouseService.getWarehouse());
    }


    @Operation(summary = "查询全部退件仓", description = "查询全部退件仓")
    @GetMapping("/warehouseNameList")
    public R warehouseNameList() {
        LambdaQueryWrapper<WarehouseEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(WarehouseEntity::getWarehouseId, WarehouseEntity::getWarehouseName, WarehouseEntity::getWarehouseCode, WarehouseEntity::getIsValid).groupBy(WarehouseEntity::getWarehouseId);
        return R.ok(warehouseService.list(wrapper));
    }

    /**
     * 导出excel 表格
     *
     * @param warehouse 查询条件
     * @param ids       导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_Warehouse_export')")
    public List<WarehouseEntity> export(WarehouseEntity warehouse, Integer[] ids) {
        LambdaQueryWrapper<WarehouseEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(warehouse.getWarehouseCode()), WarehouseEntity::getWarehouseCode, warehouse.getWarehouseCode())
                .like(StringUtils.isNotBlank(warehouse.getWarehouseName()), WarehouseEntity::getWarehouseName, warehouse.getWarehouseName())
                .in(ArrayUtil.isNotEmpty(ids), WarehouseEntity::getWarehouseId, ids);
        return warehouseService.list(wrapper);

    }
}