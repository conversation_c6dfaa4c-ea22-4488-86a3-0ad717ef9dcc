package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.PostEntity;
import com.jygjexp.jynx.basic.back.service.PostService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.PostModifyEntity;
import com.jygjexp.jynx.basic.back.service.PostModifyService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 驿站审核
 *
 * <AUTHOR>
 * @date 2024-11-19 18:03:39
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/postModify")
@Tag(description = "postModify", name = "驿站审核管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PostModifyController {

    private final PostModifyService postModifyService;

    private final PostService postService;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param postModify 驿站审核
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_PostModify_view')" )
    public R getPostModifyPage(@ParameterObject Page page, @ParameterObject PostModifyEntity postModify) {
        return R.ok(postModifyService.getPage(page, postModify));
    }


    /**
     * 通过id查询驿站审核
     *
     * @param auditId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{auditId}")
    public R getById(@PathVariable("auditId") Integer auditId) {
        return R.ok(postModifyService.getById(auditId));
    }


    @Operation(summary = "通过postId查询", description = "通过postId查询")
    @GetMapping("/getByPostId")
    public R getByPostId(@RequestParam Integer postId) {
        return R.ok(postModifyService.getByPostId(postId));
    }


    /**
     * 新增驿站审核
     *
     * @param postModify 驿站审核
     * @return R
     */
    @Operation(summary = "新增驿站审核", description = "新增驿站审核")
    @SysLog("新增驿站审核")
    @PostMapping
    public R save(@RequestBody PostModifyEntity postModify) {
        postModify.setCreateDate(LocalDateTime.now());
        return R.ok(postModifyService.saveModify(postModify));
    }

    /**
     * 修改驿站审核
     *
     * @param postModify 驿站审核
     * @return R
     */
    @Operation(summary = "修改驿站审核", description = "修改驿站审核")
    @SysLog("修改驿站审核")
    @PutMapping
    public R updateById(@RequestBody PostModifyEntity postModify) {
        return postModifyService.updatePost(postModify);
    }

    /**
     * 通过id删除驿站审核
     *
     * @param ids auditId列表
     * @return R
     */
    @Operation(summary = "通过id删除驿站审核", description = "通过id删除驿站审核")
    @SysLog("通过id删除驿站审核")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_PostModify_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(postModifyService.removeBatchByIds(CollUtil.toList(ids)));
    }

}