package com.jygjexp.jynx.basic.back.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jygjexp.jynx.basic.back.bo.QueryCondition;
import com.jygjexp.jynx.basic.back.bo.SysSmsRecordAbo;
import com.jygjexp.jynx.basic.back.entity.SysSmsRecordEntity;
import com.jygjexp.jynx.basic.back.service.SysSmsRecordService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * NB,SG,SY系统短信记录
 *
 * <AUTHOR>
 * @date 2025-08-05 15:03:13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/sysSmsRecord" )
@Tag(description = "sysSmsRecord" , name = "NB,SG,SY系统短信记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SysSmsRecordController {

    private final SysSmsRecordService sysSmsRecordService;

    /**
     * 分页查询
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    public R<IPage<SysSmsRecordEntity>> getSysSmsRecordPage(@RequestBody QueryCondition<SysSmsRecordAbo> qbo) {
        return R.ok(sysSmsRecordService.pageData(qbo));
    }


    /**
     * 通过id查询NB,SG,SY系统短信记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('basic_sysSmsRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(sysSmsRecordService.getById(id));
    }

    /**
     * 新增NB,SG,SY系统短信记录
     * @param sysSmsRecord NB,SG,SY系统短信记录
     * @return R
     */
    @Operation(summary = "新增NB,SG,SY系统短信记录" , description = "新增NB,SG,SY系统短信记录" )
    @SysLog("新增NB,SG,SY系统短信记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_sysSmsRecord_add')" )
    public R save(@RequestBody SysSmsRecordEntity sysSmsRecord) {
        return R.ok(sysSmsRecordService.save(sysSmsRecord));
    }

}