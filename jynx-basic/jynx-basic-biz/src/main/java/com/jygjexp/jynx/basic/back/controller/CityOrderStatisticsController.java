package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.CityOrderStatisticsEntity;
import com.jygjexp.jynx.basic.back.service.CityOrderStatisticsService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 城市单量统计
 *
 * <AUTHOR>
 * @date 2025-02-19 09:57:13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/cityOrderStatistics" )
@Tag(description = "cityOrderStatistics" , name = "城市单量统计管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CityOrderStatisticsController {
    private final  CityOrderStatisticsService cityOrderStatisticsService;

    /**
     * 通过id查询城市单量统计
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询城市单量统计" , description = "通过id查询城市单量统计" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(cityOrderStatisticsService.getById(id));
    }

    @GetMapping("/getCityOrderStatistics")
    @Operation(summary = "订单统计", description = "订单统计")
    @PreAuthorize("@pms.hasPermission('back_CityOrderStatistics_view')")
    public R getCityOrderList(String startTime, String endTime){
        return R.ok(cityOrderStatisticsService.getCityOrderList(startTime,endTime));
    }

}