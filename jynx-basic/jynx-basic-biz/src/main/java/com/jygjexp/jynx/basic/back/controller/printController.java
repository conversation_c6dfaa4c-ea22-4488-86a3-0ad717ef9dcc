package com.jygjexp.jynx.basic.back.controller;

import com.jygjexp.jynx.basic.back.service.PrintService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 打印机对接接口
 *
 * <AUTHOR>
 * @date 2025-03-06 17:51:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/zt/api")
@Tag(description = "cloudPrint", name = "云打印")
public class printController {

    private final PrintService printService;


    @Inner(value = false)
    @Operation(summary = "打印面单")
    @GetMapping("/print/create")
    public R create(String deviceId, String labelUrl, String orderNo, Integer postId) {
        return printService.printLabel(deviceId, labelUrl, orderNo, postId);
    }


    @Inner(value = false)
    @Operation(summary = "测试打印")
    @GetMapping("/print/createTest")
    public R createTest(String deviceId) {
        return R.ok(printService.printLabel(deviceId, null, "createTest", null));
    }


    @Inner(value = false)
    @Operation(summary = "获取设备在线状态")
    @GetMapping("/print/getStatus")
    public R getStatus(String deviceId) {
        return printService.getPrintStatus(deviceId);
    }


    @Inner(value = false)
    @Operation(summary = "获取待打印队列报文")
    @GetMapping("/print/waitingMsg")
    public ResponseEntity<String> getWaitingMsg(String deviceId) {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(printService.getWaitingMsg(deviceId));
    }


    @Inner(value = false)
    @Operation(summary = "清空打印队列")
    @GetMapping("/print/clear")
    public R printClear(String deviceId) {
        return printService.printClear(deviceId);
    }

    @Inner(value = false)
    @Operation(summary = "全局环境统一获取token接口")
    @GetMapping("/print/getToken")
    public R getToken() {
       return R.ok(printService.getTokenFromCache()) ;
    }

}
