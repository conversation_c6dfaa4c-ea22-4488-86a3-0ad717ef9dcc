package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.SubOrderEntity;
import com.jygjexp.jynx.basic.back.service.SubOrderService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;


/**
 * 订单子表
 *
 * <AUTHOR>
 * @date 2025-03-18 20:58:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/subOrder" )
@Tag(description = "subOrder" , name = "订单子表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SubOrderController {

    private final SubOrderService subOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param subOrder 订单子表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('basic_subOrder_view')" )
    public R getSubOrderPage(@ParameterObject Page page, @ParameterObject SubOrderEntity subOrder) {
        LambdaQueryWrapper<SubOrderEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(subOrderService.page(page, wrapper));
    }


    /**
     * 通过id查询订单子表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('basic_subOrder_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(subOrderService.getById(id));
    }

    /**
     * 新增订单子表
     * @param subOrder 订单子表
     * @return R
     */
    @Operation(summary = "新增订单子表" , description = "新增订单子表" )
    @SysLog("新增订单子表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_subOrder_add')" )
    public R save(@RequestBody SubOrderEntity subOrder) {
        return R.ok(subOrderService.save(subOrder));
    }

    /**
     * 修改订单子表
     * @param subOrder 订单子表
     * @return R
     */
    @Operation(summary = "修改订单子表" , description = "修改订单子表" )
    @SysLog("修改订单子表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_subOrder_edit')" )
    public R updateById(@RequestBody SubOrderEntity subOrder) {
        return R.ok(subOrderService.updateById(subOrder));
    }

    /**
     * 通过id删除订单子表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除订单子表" , description = "通过id删除订单子表" )
    @SysLog("通过id删除订单子表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_subOrder_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(subOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param subOrder 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_subOrder_export')" )
    public List<SubOrderEntity> export(SubOrderEntity subOrder,Integer[] ids) {
        return subOrderService.list(Wrappers.lambdaQuery(subOrder).in(ArrayUtil.isNotEmpty(ids), SubOrderEntity::getId, ids));
    }
}