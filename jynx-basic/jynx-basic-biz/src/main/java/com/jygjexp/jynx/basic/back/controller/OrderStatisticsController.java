package com.jygjexp.jynx.basic.back.controller;

import com.jygjexp.jynx.basic.back.entity.OrderStatisticsEntity;
import com.jygjexp.jynx.basic.back.service.OrderStatisticsService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@RestController
@RequiredArgsConstructor
@RequestMapping("/OrderStatistics")
@Tag(description = "OrderStatistics", name = "订单统计")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class OrderStatisticsController {

    private final OrderStatisticsService orderStatisticsService;


    // 根据 ID 查询
    @GetMapping("/{id}")
    public OrderStatisticsEntity getById(@PathVariable Integer id) {
        return orderStatisticsService.getById(id);
    }

    // 统计预报、返仓
    @GetMapping("/getStatistics")
    @Operation(summary = "订单统计", description = "订单统计")
    @PreAuthorize("@pms.hasPermission('back_OrderStatistics_view')")
    public R getList(Integer authId, String startTime, String endTime) {
        return R.ok(orderStatisticsService.getList(authId, startTime, endTime));
    }

    //获取合作商订单图表数据（收货）
    @Inner(value = false)
    @GetMapping("/getPostInStatistics")
    @Operation(summary = "订单统计", description = "订单统计")
    @PreAuthorize("@pms.hasPermission('back_PostInStatistics_view')")
    public R getPostList(String startTime, String endTime) {
        return R.ok(orderStatisticsService.getPostList(startTime, endTime));
    }



}
