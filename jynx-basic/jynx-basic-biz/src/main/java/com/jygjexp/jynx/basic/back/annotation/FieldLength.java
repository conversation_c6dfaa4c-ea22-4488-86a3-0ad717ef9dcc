package com.jygjexp.jynx.basic.back.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 
 * @discription String类型字段长度限制的注解。
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldLength {
	int minLength() default 0;
	int maxLength();
	String message() default "";
}
