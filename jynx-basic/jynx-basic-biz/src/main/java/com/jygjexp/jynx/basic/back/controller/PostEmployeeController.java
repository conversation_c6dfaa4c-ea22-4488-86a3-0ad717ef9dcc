package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.basic.back.api.feign.RemoteBasicAppUserService;
import com.jygjexp.jynx.basic.back.model.vo.PostEmployeeVo;
import com.jygjexp.jynx.basic.response.LocalizedR;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.PostEmployeeEntity;
import com.jygjexp.jynx.basic.back.service.PostEmployeeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 驿站员工
 *
 * <AUTHOR>
 * @date 2024-10-30 21:24:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tkzjZtPostEmployee")
@Tag(description = "tkzjZtPostEmployee", name = "驿站员工管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PostEmployeeController {

    private final PostEmployeeService postEmployeeService;
    private final RemoteBasicAppUserService remoteTmsAppUserService;

    /**
     * 分页查询
     *
     * @param page         分页对象
     * @param postEmployee 驿站员工
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_tkzjZtPostEmployee_view')" )
    public R getPostEmployeePage(@ParameterObject Page page, @ParameterObject PostEmployeeEntity postEmployee) {
        return R.ok(postEmployeeService.getPage(page, postEmployee));
    }


    /**
     * 通过id查询驿站员工
     *
     * @param employeeId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{employeeId}")
    @PreAuthorize("@pms.hasPermission('back_tkzjZtPostEmployee_view')")
    public R getById(@PathVariable("employeeId") Integer employeeId) {
        return R.ok(postEmployeeService.getById(employeeId));
    }

    /**
     * 新增驿站员工
     *
     * @param postEmployee 驿站员工
     * @return R
     */
    @Operation(summary = "新增驿站员工", description = "新增驿站员工")
    @SysLog("新增驿站员工")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_tkzjZtPostEmployee_add')" )
    public R save(@RequestBody PostEmployeeEntity postEmployee) {
        PostEmployeeEntity one = postEmployeeService.getOne(new LambdaQueryWrapper<PostEmployeeEntity>().eq(PostEmployeeEntity::getMobile, postEmployee.getMobile())
                .eq(PostEmployeeEntity::getIsValid, true));
        if (null != one){
             return R.failed("The mobile number already exists");
        }
        postEmployee.setIsValid(1);
        postEmployee.setCreateDate(LocalDateTime.now());
        if (StrUtil.isNotBlank(postEmployee.getPassword())){
            postEmployee.setPassword(postEmployee.getPassword());
        }

        // 判断传过来的app登录用户名和手机号是否重复
        R<AppUserInfo> info = remoteTmsAppUserService.info(postEmployee.getUserName());
        if (info.getCode() == 0) {
            return LocalizedR.failed("tkzj.zt.post.employee.app.userName.exist", postEmployee.getUserName());
        }

        R<AppUserInfo> infoTwo = remoteTmsAppUserService.info(postEmployee.getMobile());
        if (infoTwo.getCode() == 0) {
            return LocalizedR.failed("tkzj.zt.post.employee.app.mobile.exist", postEmployee.getMobile());
        }
        return R.ok(postEmployeeService.save(postEmployee));
    }

    /**
     * 修改驿站员工
     *
     * @param postEmployee 驿站员工
     * @return R
     */
    @Operation(summary = "修改驿站员工", description = "修改驿站员工")
    @SysLog("修改驿站员工")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_tkzjZtPostEmployee_edit')" )
    public R updateById(@RequestBody PostEmployeeEntity postEmployee) {
        return R.ok(postEmployeeService.updateById(postEmployee));
    }

    /**
     * 通过id删除驿站员工
     *
     * @param ids employeeId列表
     * @return R
     */
    @Operation(summary = "通过id删除驿站员工", description = "通过id删除驿站员工")
    @SysLog("通过id删除驿站员工")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_tkzjZtPostEmployee_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(postEmployeeService.removeBatchByIds(CollUtil.toList(ids)));
    }



    @Operation(summary = "通过手机号查询", description = "通过手机号查询")
    @GetMapping("/getByPhone/{phone}")
    public R getByPhone(@PathVariable("phone") String phone) {
        return R.ok(postEmployeeService.getByPhone(phone));
    }


    /**
     * 导出excel 表格
     *
     * @param postEmployee 查询条件
     * @param ids          导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_tkzjZtPostEmployee_export')")
    public List<PostEmployeeVo> export(PostEmployeeEntity postEmployee, Integer[] ids) {
        return postEmployeeService.getExcel(postEmployee,ids);
    }
}