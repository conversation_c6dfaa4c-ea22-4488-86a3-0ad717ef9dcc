package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.CityEntity;
import com.jygjexp.jynx.basic.back.service.CityService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.apache.logging.log4j.util.Strings;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地区
 *
 * <AUTHOR>
 * @date 2024-10-11 18:02:03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/city")
@Tag(description = "city", name = "地区管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CityController {

    private final CityService cityService;

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param city 地区
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    //@PreAuthorize("@pms.hasPermission('back_City_view')")
    public R getCityPage(@ParameterObject Page page, @ParameterObject CityEntity city) {
        LambdaQueryWrapper<CityEntity> wrapper = Wrappers.lambdaQuery();
        // 判断 city.getEnName() 是否为空，如果不为空，则添加模糊查询条件
        if (StringUtils.isNotBlank(city.getEnName())) {
            wrapper.like(CityEntity::getEnName, city.getEnName());
        }
        page.setMaxLimit(5000L);
        return R.ok(cityService.page(page, wrapper));
    }


    /**
     * 通过id查询地区
     *
     * @param cityId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{cityId}")
    @PreAuthorize("@pms.hasPermission('back_City_view')")
    public R getById(@PathVariable("cityId") Integer cityId) {
        return R.ok(cityService.getById(cityId));
    }

    /**
     * 新增地区
     *
     * @param city 地区
     * @return R
     */
    @Operation(summary = "新增地区", description = "新增地区")
    @SysLog("新增地区")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_City_add')")
    public R save(@RequestBody CityEntity city) {
        return R.ok(cityService.save(city));
    }

    /**
     * 修改地区
     *
     * @param city 地区
     * @return R
     */
    @Operation(summary = "修改地区", description = "修改地区")
    @SysLog("修改地区")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_City_edit')")
    public R updateById(@RequestBody CityEntity city) {
        return R.ok(cityService.updateById(city));
    }

    /**
     * 通过id删除地区
     *
     * @param ids cityId列表
     * @return R
     */
    @Operation(summary = "通过id删除地区", description = "通过id删除地区")
    @SysLog("通过id删除地区")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_City_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(cityService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param city 查询条件
     * @param ids  导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_City_export')")
    public List<CityEntity> export(CityEntity city, Integer[] ids) {
        return cityService.list(Wrappers.lambdaQuery(city).in(ArrayUtil.isNotEmpty(ids), CityEntity::getCityId, ids));
    }
}