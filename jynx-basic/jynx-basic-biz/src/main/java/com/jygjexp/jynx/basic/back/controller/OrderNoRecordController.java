package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.OrderNoRecordEntity;
import com.jygjexp.jynx.basic.back.service.OrderNoRecordService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * 生成的单号记录
 *
 * <AUTHOR>
 * @date 2024-10-09 23:48:51
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/orderNoRecord" )
@Tag(description = "orderNoRecord" , name = "生成的单号记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class OrderNoRecordController {

    private final OrderNoRecordService orderNoRecordService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param orderNoRecord 生成的单号记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('back_OrderNoRecord_view')" )
    public R getOrderNoRecordPage(@ParameterObject Page page, @ParameterObject OrderNoRecordEntity orderNoRecord) {
        LambdaQueryWrapper<OrderNoRecordEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(orderNoRecordService.page(page, wrapper));
    }


    /**
     * 通过id查询生成的单号记录
     * @param recordId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{recordId}" )
    @PreAuthorize("@pms.hasPermission('back_OrderNoRecord_view')" )
    public R getById(@PathVariable("recordId" ) Integer recordId) {
        return R.ok(orderNoRecordService.getById(recordId));
    }

    /**
     * 新增生成的单号记录
     * @param orderNoRecord 生成的单号记录
     * @return R
     */
    @Operation(summary = "新增生成的单号记录" , description = "新增生成的单号记录" )
    @SysLog("新增生成的单号记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_OrderNoRecord_add')" )
    public R save(@RequestBody OrderNoRecordEntity orderNoRecord) {
        return R.ok(orderNoRecordService.save(orderNoRecord));
    }

    /**
     * 修改生成的单号记录
     * @param orderNoRecord 生成的单号记录
     * @return R
     */
    @Operation(summary = "修改生成的单号记录" , description = "修改生成的单号记录" )
    @SysLog("修改生成的单号记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_OrderNoRecord_edit')" )
    public R updateById(@RequestBody OrderNoRecordEntity orderNoRecord) {
        return R.ok(orderNoRecordService.updateById(orderNoRecord));
    }

    /**
     * 通过id删除生成的单号记录
     * @param ids recordId列表
     * @return R
     */
    @Operation(summary = "通过id删除生成的单号记录" , description = "通过id删除生成的单号记录" )
    @SysLog("通过id删除生成的单号记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_OrderNoRecord_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(orderNoRecordService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param orderNoRecord 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_OrderNoRecord_export')" )
    public List<OrderNoRecordEntity> export(OrderNoRecordEntity orderNoRecord,Integer[] ids) {
        return orderNoRecordService.list(Wrappers.lambdaQuery(orderNoRecord).in(ArrayUtil.isNotEmpty(ids), OrderNoRecordEntity::getRecordId, ids));
    }
}