package com.jygjexp.jynx.basic.back.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.UniOrderEntity;
import com.jygjexp.jynx.basic.back.service.UniOrderService;
import com.jygjexp.jynx.common.core.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/uniOrder")
@Tag(description = "uniOrder", name = "有你订单管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class UniOrderController {

    private final UniOrderService uniOrderService;


    @PostMapping("/create")
    public boolean create(@RequestBody UniOrderEntity UniOrderEntity) {
        return uniOrderService.save(UniOrderEntity);
    }

    @GetMapping("/get/{id}")
    public UniOrderEntity getById(@PathVariable Integer id) {
        return uniOrderService.getById(id);
    }

    @PutMapping("/update")
    public boolean update(@RequestBody UniOrderEntity UniOrderEntity) {
        return uniOrderService.updateById(UniOrderEntity);
    }

    @DeleteMapping("/delete/{id}")
    public boolean delete(@PathVariable Integer id) {
        return uniOrderService.removeById(id);
    }


    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_Uni_view')")
    public R getUniPage(@ParameterObject Page page, @ParameterObject UniOrderEntity order) {
        return R.ok(uniOrderService.getPage(page, order));
    }

}
