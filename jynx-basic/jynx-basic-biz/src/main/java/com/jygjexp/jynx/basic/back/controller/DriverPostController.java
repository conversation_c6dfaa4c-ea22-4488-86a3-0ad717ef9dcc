package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.DriverPostEntity;
import com.jygjexp.jynx.basic.back.service.DriverPostService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 司机驿站关联
 *
 * <AUTHOR>
 * @date 2024-11-12 10:16:31
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/driverPost")
@Tag(description = "driverPost", name = "司机驿站关联管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DriverPostController {

    private final DriverPostService driverPostService;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param driverPost 司机驿站关联
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    public R getDriverPostPage(@ParameterObject Page page, @ParameterObject DriverPostEntity driverPost) {
        LambdaQueryWrapper<DriverPostEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(driverPostService.page(page, wrapper));
    }


    /**
     * 通过id查询司机驿站关联
     *
     * @param driverPostId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{driverPostId}")
    public R getById(@PathVariable("driverPostId") Integer driverPostId) {
        return R.ok(driverPostService.getById(driverPostId));
    }

    @Operation(summary = "驿站组分配司机", description = "驿站组分配司机")
    @SysLog("驿站组分配司机")
    @PostMapping("/saveRelationDriver")
    public R save(@RequestBody DriverPostEntity driverPost) {
        return driverPostService.saveRelationDriver(driverPost);
    }


    @Operation(summary = "驿站组取消司机", description = "驿站组取消司机")
    @SysLog("驿站组取消司机")
    @DeleteMapping("/deleteRelationDriver")
    public R deleteRelationDriver(@RequestBody DriverPostEntity driverPost) {
        return driverPostService.deleteRelationDriver(driverPost);
    }


    @Operation(summary = "驿站组分配驿站", description = "驿站组分配驿站")
    @SysLog("驿站组分配驿站")
    @PostMapping("/saveRelationPost")
    public R saveRelationPost(@RequestBody DriverPostEntity driverPost, String postIds) {
        return driverPostService.saveRelationPost(driverPost, postIds);
    }


    @Operation(summary = "驿站组取消驿站", description = "驿站组取消驿站")
    @SysLog("驿站组取消驿站")
    @DeleteMapping("/deleteRelationPost")
    public R deleteRelationPost(@RequestBody DriverPostEntity driverPost, String postIds) {
        return driverPostService.deleteRelationPost(driverPost, postIds);
    }

    /**
     * 修改司机驿站关联
     *
     * @param driverPost 司机驿站关联
     * @return R
     */
    @Operation(summary = "修改司机驿站关联", description = "修改司机驿站关联")
    @SysLog("修改司机驿站关联")
    @PutMapping
    public R updateById(@RequestBody DriverPostEntity driverPost) {
        return R.ok(driverPostService.updateById(driverPost));
    }

    /**
     * 通过id删除司机驿站关联
     *
     * @param ids driverPostId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机驿站关联", description = "通过id删除司机驿站关联")
    @SysLog("通过id删除司机驿站关联")
    @DeleteMapping
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(driverPostService.removeBatchByIds(CollUtil.toList(ids)));
    }


    @Operation(summary = "查询所有已经被驿站组分配的驿站", description = "查询所有已经被驿站组分配的驿站")
    @SysLog("查询所有已经被驿站组分配的驿站")
    @GetMapping("getPosts")
    public R getPosts() {
        return R.ok(driverPostService.getPosts());
    }

}