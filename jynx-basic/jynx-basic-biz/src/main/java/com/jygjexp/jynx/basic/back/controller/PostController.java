package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.PhotoEntity;
import com.jygjexp.jynx.basic.back.model.vo.excel.PostExcelVo;
import com.jygjexp.jynx.basic.back.service.OrderService;
import com.jygjexp.jynx.basic.back.service.ZtPhotoService;
import com.jygjexp.jynx.basic.back.model.vo.PostPageVo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.PostEntity;
import com.jygjexp.jynx.basic.back.service.PostService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 驿站
 *
 * <AUTHOR>
 * @date 2024-10-12 22:38:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/post")
@Tag(description = "post", name = "驿站管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PostController {

    private final PostService postService;

    private final ZtPhotoService photoService;

    private final OrderService orderService;

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_Post_view')")
    public R getPostPage(@ParameterObject Page page, @ParameterObject PostEntity post) {
        return R.ok(postService.getPage(page, post));
    }

    @Operation(summary = "查询所有有效驿站", description = "查询所有有效驿站")
    @GetMapping("/getValidPostInfo")
    public R getValidPostInfo() {
        LambdaQueryWrapper<PostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PostEntity::getIsValid, true);
        return R.ok(postService.list(wrapper));
    }


    @Operation(summary = "查询所有未分配活动的驿站", description = "查询所有未分配活动的驿站")
    @GetMapping("/getNoActivityPost")
    public R getNoActivityPost() {
        LambdaQueryWrapper<PostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PostEntity::getIsValid, true)
                .isNull(PostEntity::getActivityId);
        return R.ok(postService.list(wrapper));
    }

    @Operation(summary = "查询所有已分配活动的驿站", description = "查询所有已分配活动的驿站")
    @GetMapping("/getActivityPost")
    public R getActivityPost() {
        LambdaQueryWrapper<PostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PostEntity::getIsValid, true)
                .isNotNull(PostEntity::getActivityId);
        return R.ok(postService.list(wrapper));
    }


    @Operation(summary = "查询驿站组未关联驿站", description = "查询驿站组未关联驿站")
    @GetMapping("/getNoRelationPostInfo")
    @PreAuthorize("@pms.hasPermission('back_PostGroup_info')")
    public R getNoRelationPostInfo(@RequestParam Integer warehouseId) {
        LambdaQueryWrapper<PostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PostEntity::getIsValid, true);
        wrapper.eq(PostEntity::getGroupId, 0);
        wrapper.eq(PostEntity::getWarehouseId, warehouseId);
        return R.ok(postService.list(wrapper));
    }


    /**
     * 通过id查询驿站
     *
     * @param postId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{postId}")
    public R getById(@PathVariable("postId") Integer postId) {
        return R.ok(photoService.getDetail(postId));
    }

    /**
     * 新增驿站
     *
     * @param post 驿站
     * @return R
     */
    @Operation(summary = "新增驿站", description = "新增驿站")
    @SysLog("新增驿站")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_Post_add')")
    public R save(@RequestBody PostEntity post) {
        return postService.savePost(post);
    }

    /**
     * 修改驿站
     *
     * @param post 驿站
     * @return R
     */
    @Operation(summary = "修改驿站", description = "修改驿站")
    @SysLog("修改驿站")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_Post_edit')")
    public R updateById(@RequestBody PostEntity post) {
        return postService.updatePost(post);
    }

    /**
     * 通过id删除驿站
     *
     * @return R
     */
    @Operation(summary = "通过id删除驿站", description = "通过id删除驿站")
    @SysLog("通过id删除驿站")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_Post_del')")
    public R removeById(@RequestParam("postId") String postId) {
        postService.removeById(postId);
        photoService.remove(new QueryWrapper<PhotoEntity>().eq("post_id", postId));
        return R.ok(postService.removeById(postId));
    }

    @Operation(summary = "通过PhotoId删除指定图片", description = "通过PhotoId删除指定图片")
    @SysLog("通过PhotoId删除指定图片")
    @DeleteMapping("/removeByPhotoId")
    public R removeByPhotoId(@RequestParam("photoIds") String photoIds) {
        return photoService.deleteBatchIds(photoIds);
    }


    @Operation(summary = "查询驿站各类订单数量", description = "查询驿站各类订单数量")
    @GetMapping("/getOrderNum")
    public R getOrderNum() {
        return R.ok(orderService.getOrderNum());
    }


    @Operation(summary = "查询全部驿站名称" , description = "查询全部驿站名称" )
    @GetMapping("/postNameList")
    public R driverNameList() {
        LambdaQueryWrapper<PostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(PostEntity::getPostId, PostEntity::getPostName, PostEntity::getIsValid).groupBy(PostEntity::getPostId);
        return R.ok(postService.list(wrapper));
    }


    /**
     * 导出excel 表格
     *
     * @param post 查询条件
     * @param ids  导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_Post_export')")
    public List<PostExcelVo> export(PostEntity post, Integer[] ids) {
        return postService.getExcel(post, ids);
    }
}