package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.service.OrderService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.DriverEntity;
import com.jygjexp.jynx.basic.back.service.DriverService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 司机
 *
 * <AUTHOR>
 * @date 2024-11-08 18:30:43
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/driver")
@Tag(description = "driver", name = "司机管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DriverController {

    private final DriverService driverService;
    private final OrderService orderService;

    /**
     * 分页查询
     *
     * @param page   分页对象
     * @param driver 司机
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_Driver_view')" )
    public R getDriverPage(@ParameterObject Page page, @ParameterObject DriverEntity driver) {
        return R.ok(driverService.getPage(page, driver));
    }


    /**
     * 通过id查询司机
     *
     * @param driverId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{driverId}")
    @PreAuthorize("@pms.hasPermission('back_Driver_view')" )
    public R getById(@PathVariable("driverId") Integer driverId) {
        return R.ok(driverService.getById(driverId));
    }

    @Operation(summary = "通过手机号查询", description = "通过手机号查询")
    @GetMapping("/getByPhone/{phone}")
    public R getByPhone(@PathVariable("phone") String phone) {
        return driverService.getByPhone(phone);
    }

    /**
     * 新增司机
     *
     * @param driver 司机
     * @return R
     */
    @Operation(summary = "新增司机", description = "新增司机")
    @SysLog("新增司机")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_Driver_add')" )
    public R save(@RequestBody DriverEntity driver) {
        return R.ok(driverService.save(driver));
    }

    /**
     * 修改司机
     *
     * @param driver 司机
     * @return R
     */
    @Operation(summary = "修改司机", description = "修改司机")
    @SysLog("修改司机")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_Driver_edit')" )
    public R updateById(@RequestBody DriverEntity driver) {
        return R.ok(driverService.updateById(driver));
    }

    /**
     * 通过id删除司机
     *
     * @param ids driverId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机", description = "通过id删除司机")
    @SysLog("通过id删除司机")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_Driver_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(driverService.removeBatchByIds(CollUtil.toList(ids)));
    }

    //司机取件排行榜
    @Operation(summary = "司机取件排行榜", description = "司机取件排行榜")
    @GetMapping("/getDriverOrder")
    @PreAuthorize("@pms.hasPermission('back_DriverRank_view')")
    public R getDriverOrder(String time) {
        return driverService.getDriverRank(time);
    }


    /**
     * 导出excel 表格
     *
     * @param driver 查询条件
     * @param ids    导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_Driver_export')")
    public List<DriverEntity> export(DriverEntity driver, Integer[] ids) {
        return driverService.list(Wrappers.lambdaQuery(driver).in(ArrayUtil.isNotEmpty(ids), DriverEntity::getDriverId, ids));
    }
}