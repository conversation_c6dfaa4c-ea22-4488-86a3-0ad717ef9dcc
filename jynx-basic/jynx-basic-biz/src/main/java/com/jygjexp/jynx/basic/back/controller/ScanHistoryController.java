package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.ScanHistoryEntity;
import com.jygjexp.jynx.basic.back.service.ScanHistoryService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 扫描历史
 *
 * <AUTHOR>
 * @date 2024-11-15 16:34:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/scanHistory")
@Tag(description = "scanHistory", name = "扫描历史")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ScanHistoryController {

    private final ScanHistoryService scanHistoryService;

    /**
     * 分页查询
     *
     * @param page        分页对象
     * @param scanHistory 扫描历史
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    //@PreAuthorize("@pms.hasPermission('basic_scanHistory_view')" )
    public R getScanHistoryPage(@ParameterObject Page page, @ParameterObject ScanHistoryEntity scanHistory, Integer warehouseId) {
        return R.ok(scanHistoryService.getPage(page, scanHistory, warehouseId));
    }


    /**
     * 更新测量结果
     *
     * @param scanHistory
     * @return
     */
    @Operation(summary = "更新扫描结果", description = "更新扫描结果")
    @PostMapping("/updateMeasurement")
    public R updateMeasurementById(@ParameterObject ScanHistoryEntity scanHistory) {
        return scanHistoryService.updateMeasurement(scanHistory);
    }



    /**
     * 修改异常订单
     *
     * @param scanHistory
     * @return
     */
    @Operation(summary = "修改异常订单", description = "修改异常订单")
    @PostMapping("/updateExceptionOrder")
    public R updateExceptionOrder(@ParameterObject ScanHistoryEntity scanHistory) {
        return scanHistoryService.updateExceptionOrder(scanHistory);
    }





}


