package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: xiongpengfei
 * @Description: 服务商利润配置分页参数
 * @Date: 2025/8/26 11:08
 */
@Data
@Schema(description = "服务商利润配置分页参数")
public class TmsStoreProfitConfigPageVo {

    /**
     * 服务商id
     */
    @Schema(description="服务商id")
    private Long providerId;

    /**
     * 启用状态：0：禁用、1：启用
     */
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

    /**
     * 规则类型
     */
    @Schema(description="规则类型 0-百分比 1-固定值 2-绝对价格")
    private Integer ruleType;

    /**
     * 创建时间开始
     */
    @Schema(description="创建时间开始")
    private String startTime;

    /**
     * 创建时间结束
     */
    @Schema(description="创建时间结束")
    private String endTime;


}
