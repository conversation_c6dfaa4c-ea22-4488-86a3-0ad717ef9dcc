package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 服务商管理表
 *
 * <AUTHOR>
 * @date 2025-07-09 17:03:42
 */
@Data
@TenantTable
@TableName("tms_service_provider")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务商管理表")
public class TmsServiceProviderEntity extends BaseLogicEntity<TmsServiceProviderEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long providerId;

	/**
	* 服务商代码
	*/
    @Schema(description="服务商代码")
    private String providerCode;

	/**
	* 服务商名称
	*/
    @Schema(description="服务商名称")
    private String providerName;

	/**
	* AppKey
	*/
    @Schema(description="AppKey")
    private String appKey;

	/**
	* Token
	*/
    @Schema(description="Token")
    private String token;

	/**
	* ClientID
	*/
    @Schema(description="ClientID")
    private String clientId;

	/**
	* ClientSecret
	*/
    @Schema(description="ClientSecret")
    private String clientSecret;

	/**
	* 账户
	*/
    @Schema(description="账户")
    private String providerUsername;

	/**
	* 密码
	*/
    @Schema(description="密码")
    private String providerPassword;

	/**
	 * 材积系数
	 */
   	private Integer volumeCoefficient;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}