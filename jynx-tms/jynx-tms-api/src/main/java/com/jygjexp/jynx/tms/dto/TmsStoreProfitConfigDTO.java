package com.jygjexp.jynx.tms.dto;

import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: xiongpengfei
 * @Description: 服务商利润配置分页返回参数
 * @Date: 2025/8/26 11:22
 */
@Data
@Schema(description = "服务商利润配置分页返回参数")
public class TmsStoreProfitConfigDTO extends TmsStoreProfitConfigEntity {
    /**
     * 服务商代码
     */
    @Schema(description="服务商代码")
    private String providerCode;

    /**
     * 服务商名称
     */
    @Schema(description="服务商名称")
    private String providerName;

    /** 重量段展示 例如 1-20 KG */
    private String weightRange;
}
