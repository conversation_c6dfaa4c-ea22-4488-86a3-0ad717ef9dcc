package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单价格计算请求值对象
 * 专门用于价格计算的轻量级订单数据传输对象，仅包含价格计算所需的核心字段
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@Schema(description = "订单价格计算请求值对象")
public class TmsOrderPriceCalculationVo {

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Long id;

    /**
     * 客户单号/客户参考单号
     */
    @Schema(description = "客户单号/客户参考单号")
    private String customerOrderNumber;

    /**
     * 委托单号
     */
    @Schema(description = "委托单号")
    private String entrustedOrderNumber;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空")
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 始发地
     */
    @Schema(description = "始发地")
    private String origin;

    /**
     * 目的地
     */
    @Schema(description = "目的地")
    private String destination;

    /**
     * 发货邮编
     */
    @Schema(description = "发货邮编")
    @NotBlank(message = "发货地邮编不能为空")
    private String shipperPostalCode;

    /**
     * 目的地邮编
     */
    @NotBlank(message = "目的地邮编不能为空")
    @Schema(description = "目的地邮编")
    private String destPostalCode;

    /**
     * 总重量(kg)
     */
    @Schema(description = "总重量(kg)")
    private BigDecimal totalWeight;

    /**
     * 总体积(m³)
     */
    @Schema(description = "总体积(m³)")
    private BigDecimal totalVolume;

    /**
     * 每个子单的信息（长cm、宽cm、高cm、重量kg、体积m³）
     */
    @Schema(description = "每个子单的信息（长cm、宽cm、高cm、重量kg、体积m³）")
    private List<TmsSubOrderInfoVo> subOrderInfos;

    /**
     * 货物类型：1=普通货物，2=危险货物
     */
    @Schema(description="货物类型：1=普通货物，2=危险货物")
    private Integer cargoType;


    /**
     * 体积重量(kg) - 计算应收记录会直接传入，询价需要额外计算
     */
    @Schema(description = "体积重量(kg)")
    private BigDecimal volumeWeight;

    /**
     * 实际重量(kg) - 计算应收记录会直接传入，询价需要额外计算
     */
    @Schema(description = "实际重量(kg)")
    private BigDecimal actualWeight;

    /**
     * 订单扫描时间 - 计算应收记录会直接传入，询价不需要
     */
    @Schema(description = "订单扫描时间")
    private LocalDateTime scanTime;

}
