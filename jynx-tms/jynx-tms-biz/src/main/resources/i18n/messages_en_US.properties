error.database.access=System access is busy, please try again later.
error.database=System access is busy, please try again later.
error.invalid.argument=Request parameter is invalid: {0}.
error.service.unavailable=Service unavailable, please try again later: {0}.
error.unauthorized=Unauthorized, please provide valid authentication information.
error.access.denied=Access denied, insufficient permissions.
error.method.not.allowed=Request method not allowed.
error.internal.server=The server is busy. Please try again later.
error.database.insert.error=Operation failed, please check：{0}.
error.area.insert.error=Region name is duplicated.
error.warehouse.insert.error=warehouse name is duplicated.
Order.status.is.not.available.for.modification=Order status is not available for modification.

tms.exception.handling.error=Failed to process the abnormal result
tms.exception.reporting.content.is.empty=The exception content is empty
tms.exception.reporting.failed=Exception report failed, please check

tms.no.corresponding.shipping.rule.was.found=No pallet freight rule found
tms.no.valid.base.shipping.template.found=No valid base shipping template found
tms.no.matching.package.shipping.rules.were.found=No matching package shipping rules were found
tms.origin.or.destination.error=The origin or destination format is incorrect, please refer to: Country/province/city
tms.Unique.post.names.required=The post name already exists
tms.Unique.post.code.required=The post code already exists
tms.customer.order.already.exists=The customer order already exists
tms.BasicFreightPkgDetail.startWeight.le.endWeight=The cut-off weight cannot be less than the starting weight
tms.BasicFreightPkgDetail.weight.interval.repeats=The weight interval already exists
tms.imported.template.is.incorrect=The imported template is incorrect. Please use the latest version of the template.
tms.app.driver.register.error=Driver addition failure：{0}
tms.app.warehouse.register.error=The addition of warehouse staff failed：{0}
tms.app.driver.update.error=Driver modification failure
tms.app.warehouse.update.error=The warehouse staff failed to make the modification.
tms.app.warehouse.employee.update.error=The modification of personal information for warehouse staff failed.
tms.app.driver.register.poll.code.error=Registration code error:{0}
tms.app.driver.register.phone.repetition=Mobile phone number already exists in the system, please re-enter:{0}
tms.app.driver.add.phone.repetition=The driver's mobile phone number already exists in the APP terminal account and cannot be used to create a new account:{0}
tms.app.driver.update.not.vehicle.info = The driver has not bound a vehicle. Please contact the administrator to bind it.
tms.app.driver.register.email.and.ID.repetition=The mobile phone number has been registered. Please enter it again: {0}
tms.app.driver.scan.pick.error={0}-Not within the scope of this pickup
tms.app.driver.scan.pick.repetition={0}-Already picked up
tms.app.driver.scan.pick.succeed={0}-Successfully picked up the goods
tms.app.driver.order.submit.succeed={0}-Submitted successfully!
tms.app.driver.waybill.scan.error={0}-This waybill has already been scanned for pickup, no further scanning for pickup is allowed!
tms.app.driver.order.not.scan.pickup=The following waybill number has not been scanned yet：{0}
tms.app.driver.order.scan=Order not scanned：{0}
tms.app.driver.storage.order.no.error=The order number already exists in the storage record：{0}
tms.app.driver.warehouse.order.enty=This order does not belong to the current warehouse：{0}
tms.app.driver.order.not.storage=This order has not been warehoused or has been dispatched：{0}
tms.app.update.scan.status.error=Failed to update scan status
tms.app.driver.order.not.found=The line haul order not found: {0}
tms.app.driver.order.update.status.error=Failed to update the customer order status
tms.app.order.not.bound.to.line=The order is not bound to the line haul
tms.app.sub.order.not.exist=The scanned order exists. Please try again.
tms.app.main.order.not.exist=The main order does not exist
tms.app.warehouse.employee.exist=The name or mobile phone number of the warehouse staff already exists in the system. Please re-enter：{0}
tms.app.driver.register.driverNum.repetition=The driver number already exists in the system. Please re-enter ：{0}
tms.app.driver.sub.order.not.scan=There are unscanned orders: {0}


tms.zip.zone.route.number.exists=The route number already exists
tms.zip.zone..Excel.file.processing.success=Postcode partition Excel file processing was successful, a total of {0} processed
tms.zip.zone..Excel.file.processing.exception=Zip partition Excel file handling exception: {0}
tms.zip.zone.file.processing.errors=Zip partition import failed, please check the message: {0}

tms.carrier.add.repetition=The name of this carrier already exists: {0}
tms.carrier.register.error={0} Carrier failed to add, please check


tms.customer.name.exists=The customer name already exists in user management and cannot create a client account：{0}
tms.customer.phone.exists=The customer's mobile phone number already exists in the user management system, and the account cannot be created：{0}
tms.customer.register.error=Customer addition failed, please check
tms.customer.name.phone.already.exists=The customer's mobile phone number or user name already exists in the system. Please enter a new one
tms.customer.update.error=Customer failed to modify, please check：{0}
tms.customer.update.password.error=Customer password modification failed. Please check：{0}
update.password.failed=The password update failed！

tms.app.driver.cage.not.exist= Please check the scanning information or try again：{0}
tms.app.driver.cage.order.not.exist=The order does not exist：{0}
tms.app.driver.order.pickup.proof.not.exist=This order has not been picked up yet：{0}
tms.app.driver.order.delivery.proof.exist={0}-The delivery voucher has been uploaded for this order
tms.app.driver.collect.order.not.exist=The driver does not have any orders in delivery：{0}
tms.app.report.order.no.empty=The report order number cannot be empty
tms.app.report.order.not.exist=Unable to find the task order corresponding to the report number: {0}
tms.app.driver.report.order.not.exist=There is no order corresponding to the report number, please check: {0}
tms.app.driver.cage.order.not.complete=There are no unfinished task orders from drivers
tms.app.driver.has.returned.order=This order has already been marked as a failed delivery order：{0}
tms.app.driver.has.returned.warehouse.order=The order is already in the pending return to warehouse or returned to warehouse status and cannot be operated：{0}
tms.app.driver.It.has.been.delivered.three.times=The delivery of the following order has been attempted three times and has failed. Please return it to the warehouse：{0}
tms.app.driver.has.returned.order.not.delivery=The following order statuses do not allow for re-delivery (completed or returned to warehouse)：{0}
tms.app.driver.has.returned.order.not.cannot.delivery=The following order statuses do not allow the "Unable to Deliver" operation (the "Delivery Failure" status is the only one that can be operated)：{0}


tms.app.driver.has.complete.order=The order has been completed：{0}
tms.app.driver.has.pickup.order=Please check if the order is awaiting pickup：{0}
tms.app.driver.cage.order.record.exist=The order has a record of being placed in the cage：{0}


tms.user.sms.error=The text message sending failed. Please try again later{0}
tms.user.sms.exception=The text message sending is abnormal. Please try again later{0}

tms.cage.label.cannot.be.deleted = This tag code has been used in trunk transportation or has been completed and cannot be deleted:{0}




//路径规划国际化
route_planning.shipment_completed=The shipment order has already been completed and does not need further route planning.
route_planning.shipment_not_found=No shipment order information found!
route_planning.route_already_planned=The shipment order has already been planned!
route_planning.driver_not_found=Driver information not found!
route_planning.vehicle_not_found=Driver's vehicle information not found!
route_planning.token_obtain_failed=Failed to obtain Google Maps authorization token
route_planning.network_fluctuation=Network fluctuation, please try again later!
route_planning.invalid_input_parameters=There is a problem with the planning input parameters!
route_planning.request_failed=Google Maps route planning request failed
route_planning.success=Route planning successful
route_planning.failure_reason=Route planning failed - reason: {0}
route_planning.no_route_planned=No route planning has been done for this shipment order
route_planning.no_routes_planned=No route planning has been done for these shipment orders
route_planning.not_planned_yet=This shipment order has not been planned yet!
route_planning.skip_successful=Skip successful!
route_planning.entrusted_order_not_found=Entrusted order not found!
route_planning.save_status_successful=Save status successful!
route_planning.save_failed=Save failed!
route_planning.stop_receiving_orders_successful=Stop receiving orders successful!
route_planning.stop_receiving_orders_failed=Stop receiving orders failed!
route_planning.shipment_does_not_exist=The shipment order does not exist!
route_planning.address_range_too_large=Planning address range is too large
route_planning.duplicate_route_name=The name of the first step delivery batch cannot be duplicated!
route_planning.missing_delivery_warehouse=The delivery warehouse of the order has not been matched!
route_planning.different_warehouse_areas=Please select delivery orders from the same area warehouse for route planning!
route_planning.empty_delivery_warehouse=The delivery warehouse of the order is empty!
route_planning.area_not_exists=The area corresponding to the order's delivery warehouse does not exist!
route_planning.site_not_exists=The order's delivery warehouse does not exist!
route_planning.driver_no_vehicle=Driver is not bound to vehicle information!
route_planning.route_planning_failed=Route planning failed!
route_planning.empty_orders=Selected orders cannot be empty!
route_planning.task_order_not_exists=Task order does not exist!
route_planning.route_info_not_exists=Route information does not exist!
route_planning.pre_route_plan_not_exists=Pre-route plan does not exist!
order.delivery.location.empty=Delivery location coordinates are empty for order number {0}
warehouse.delivery.location.empty=Delivery coordinates for warehouse {0} are empty
route_planning.over_area_not_exists=The coverage area does not exist!

route_planning.duplicate_route=The route number {1} of batch {0} has already been planned!
batch.not_exists=The selected batch does not exist!
batch.no_orders=There are no orders in this batch!
batch.route_number_not_bound=The batch orders are not bound to a route number!

//委托订单
tms.customer.tracking.number.cannot.be.empty=Customer tracking number cannot be empty
tms.Driver.ID.is.invalid=Driver ID is invalid
tms.Driver.not.enabled.or.empty=Driver not enabled
tms.Driver.not.started=The driver has not started work and cannot be assigned
tms.entrusted.order.number.is.invalid=The entrusted order number is invalid
tms.order.status.is.not.operable=The order status is not operable
tms.Driver.not.vehicle=Driver not bound to vehicle, unable to generate transportation document
tms.driver.not.app.account.and.cannot.send.messages=The driver has not bound an app account and cannot send messages
tms.Number.successfully.allocated.orders=Number of successfully allocated orders：{0}
tms.Abnormal.allocation.of.drivers=Abnormal allocation of drivers, please check
tms.not.operable.order=There are no orders that meet the criteria for assigning drivers

//客户订单
tms.failed.to.save.customer.order=Failed to save customer order!
tms.box.weight.exceeded=Delivery of medium and large items, the maximum weight per box cannot exceed 30kg.
tms.package.weight.exceeded=Medium and large item delivery, the maximum weight of a single package cannot exceed 30kg.
tms.total.weight.exceeded=The maximum weight cannot exceed the total weight.
tms.successfully.created.customer.order=Successfully created customer order!
tms.failed.to.create.customer.order=Failed to create customer order!
tms.customer.order.number.cannot.be.empty=Customer order number cannot be empty!
tms.carrier.ID.is.invalid=Invalid carrier ID!
tms.carrier.is.invalid=Carrier not activated or empty!
tms.customer.order.number.is.invalid=Customer order number is invalid!
tms.current.order.status.is.not.operable=The current order status is not operable!
tms.no.within.carrier.service.area=CustomerOrderNumber [ {0} ] Not within the carrier's service area.
tms.anomalous.allocation.of.carriers=Abnormal allocation of carrier.
tms.order.creation.failed=Order creation failed!
tms.the.order.does.not.exist=The order does not exist!
tms.already.has.routing.information=This order already has routing information and cannot be deleted!
tms.customer.name.cannot.contain.chinese=Account name cannot contain Chinese characters!
tms.user.name.already.exists=The username already exists in this system
tms.transportation.order.successful=Conversion successful!
tms.transportation.order.failed=Conversion failed!
tms.only.pending.allocation=Only the state of pending allocation can be operated
tms.invalid.date.range=Start time cannot be greater than end time
tms.failed.to.get.delivery.warehouse.id=Unable to obtain collection or delivery warehouse
tms.No.order.tracking.info=No order tracking info.
tms.one.ticket.multiple.pieces.not.supported=The current customer only supports one ticket per item

tms.customer.phone.already.exists=The phone number already exists in the system

//客户
tms.customer.not.exist=The customer does not exist!
tms.failed.to.create.storage.record=Failed to create storage record!
tms.failed.to.create.outbound.record=Failed to create outbound record!
tms.successfully.created.record=Successfully created record!

tms.warehouse.positive.number=The size of the warehouse must be a positive number

//车辆
tms.vehicle.info.license.plate.repeat=The vehicle license plate number already exists
tms.vehicle.info.contact.phone.repeat=The vehicle contact phone number already exists
tms.vehicle.driver.already.assigned=Driver {0} has already been bound to a vehicle and cannot be reassigned.

//站点
tms.site.name.code.exists=The site name or site code already exists
tms.site.address.exists=The site address already exists

//运输任务单
tms.cargo.info.empty=The cargo information cannot be empty
tms.failed.to.create.transport.task.order=Failed to create transport task order!
tms.site.empty=Unable to find warehouse, please check postal code: {0}
tms.invalid.receive.type=Invalid receive type
tms.customer.id.empty=The customer ID cannot be empty
tms.get.warehouse.failed=Failed to get warehouse!
tms.delivery.task.created.failed=The delivery task creation failed!
tms.pickup.task.created.failed=The pickup task creation failed!
tms.not.exist.task.order=Unable to find a customer order that meets the criteria!
tms.order.already.assigned=This order has already been assigned and bound to a driver, and cannot be bound again!
tms.order.site.not.same=The origin warehouse of these orders must be the same warehouse!
tms.receive.type.not.support=The order has the delivery method set as "Delivery to Warehouse", and no collection task can be assigned. Order number：{0}
tms.pickup.task.assigned.success=The collection task assignment succeeded! {0}
tms.driver.no.vehicle=Driver not bound to vehicle, unable to assign! {0}
tms.driver.rest.failed=The driver has taken a break, please select a new driver! {0}
tms.tracking.number.cannot.be.empty=Tracking number cannot be empty
tms.transfer.success=Tracking order successfully converted to order
tms.no.allowed.transfer=Order assignment not completed, no transfer allowed!
tms.order.status.already.changed=Only orders in the waiting state can be assigned drivers! {0}

//干线
tms.line.task.order.successful=The mainline task order has been successfully created
tms.line.task.order.failed=The mainline task order creation failed
tms.not.exist.line.task.order=The mainline task order does not exist!
tms.line.task.order.not.exist=The mainline task order does not exist!
tms.line.task.order.update.failed=The mainline task order update failed!
tms.line.task.order.update.success=The mainline task order update succeeded!
tms.line.task.not.greater.than.zero=The quantity, volume, and weight cannot be less than 0
tms.line.task.only.pending.allocation=Only the state of pending allocation can be operated
tms.origin.warehouse.equals.dest.warehouse=The origin warehouse cannot be the same as the destination warehouse
tms.order.some.not.bindable=Some orders cannot be bound
tms.app.driver.label.already.scanned=Label has already been scanned.
tms.app.driver.label.not.all.scanned=The batch still has labels that have not been scanned!
tms.driver.not.exist=Driver does not exist!
tms.vehicle.not.exist=Vehicle does not exist!
tms.app.driver.label.not.exist=The label does not exist！
tms.app.driver.start.site.not.exist=The start site for label does not exist!
tms.app.driver.label.task.in.progress=This label's task is in progress or has already been completed. Scanning is not allowed!
error.lineHaulOrder.notEmpty=The selected line haul order cannot be empty!
error.lineHaulOrder.completedExists=Some selected line haul orders are already completed. Please check!
error.unassigned.driver=There are干线 tasks in the selected list that have not been assigned to a driver. The transfer operation cannot be performed. Please check!


//客户端
account.not.opened=Your account has not been opened, please contact the administrator to open an account!
email.format.invalid=Email format is incorrect!
mobile.format.invalid=Mobile format is incorrect!
verification.code.invalid=Verification code is invalid or expired!
sms.verification.code.invalid=SMS verification code is invalid or expired!
real.name.authentication.required=Real name authentication is required. Please complete real name authentication first. You cannot perform this operation in your current status!
selected.order.not.null=Selected order cannot be null!
update.mobile.failed=Update mobile failed!
update.email.failed=Update email failed!
account.not.approved=The account has not been approved. Please contact the administrator for approval before performing this operation!
phone.not.registered=The phone number is not registered in the system!
phone.already.registered=The phone number has already been registered by another user. Duplicate registration is not allowed!
phone.already.bound=The phone number is already bound to another account. Modification is not allowed!
email.already.registered=This email is already registered.
register.failed=register.failed！
old.password.incorrect=The old password is incorrect!


//异常
tms.exception.taskOrder.already.uploaded=This task order has already uploaded an exception!

//标签管理
tms.label.code.clash=There is a conflict in the label encoding generation. Please try again later

verification.code.has.expired.please.obtain.the.verification.code.again=The graphic verification code has expired. Please obtain the verification code again
verification.code.is.incorrect=The graphic verification code is incorrect

tms.app.driver.cage.order.not.mismatching=The location and route of the cage car information do not match the order information ：{0}
//格口管理
tms.app.driver.grid.order.not.mismatching=The three character postal code [{0}] is not within the coverage area
tms.area.not.match.postal.code=The coverage area corresponding to this postal code does not exist
tms.sorting.successful=Sorting successful!
tms.no.sorting.order=Pending and completed orders cannot be sorted
tms.no.order.info=No relevant order information found
tms.collection.task.not.completed=The following orders have unfinished collection tasks, as follows:{0}
tms.invalid.order=There are invalid orders present: {0}
tms.tracking.number.not.compliant=The tracking number: {0} is non compliant and should be the waybill number
tms.grid.only.one.routeNumber=The routeNumber has already maintained the grid, please do not repeat the maintenance
tms.grid.code.or.name.exists=Grid code or name already exists, please check
tms.no.sorting.grid=The warehouse: {0} does not maintain sorting gates, please check
tms.batch.no.exist=The order number has already been assigned to a batch and cannot be sorted again. Order Number: {0}

//地址簿
tms.duplicate.address=The contact address already exists

//订单批次
tms.orderbatch.created.successful=The order batch has been created successfully!
tms.orderbatch.select.successful=Batch selection successful.
tms.not.exist.order.batchno=The order batch does not exist!
tms.orderbatch.updated.failed=The order batch update failed!
tms.null.area.order.batchno=The delivery area data for the selected order's destination is empty
tms.batchno.repeat=batchno already exists

//轨迹节点维护
tms.track.node.context.repetition=Node status code or duplicate triggered nodes

//app揽收订单
tms.collection.batch.pickup.error=The following orders do not meet the upload requirements (not all scanned or status is "to be collected")：{0}

//官网轨迹验证码
tms.captcha.has.been.generated=Captcha has been generated: {0}
tms.captcha.not.null=Order number and captcha cannot be empty
tms.captcha.has.expired=The captcha has expired, please obtain it again: {0}
tms.captcha.error=Captcha error: {0}
tms.captcha.verified=Captcha verification passed: {0}


//web端司机上传揽收POD
tms.web.collent.upload.error=This order has not been picked up yet. Please try again：{0}

//返仓自提
# Error messages - English

tms.store.name.phone.already.exists=The store name or contact information already exists, please re-enter
tms.store.delete.failed=Failed to delete store
tms.store.id.empty=The store cannot be empty
tms.store.employee.create.fail=Failed to create employee {0}
tms.store.code.already.exists=The store code already exists, please re-enter {0}
tms.store.customer.code.already.exists=The customer code already exists, please re-enter {0}
tms.order.has.been.written.off=The order has been verified and cannot be canceled
tms.order.cancel.failed=Failed to cancel order {0}
tms.store.order.not.exist=Order does not exist
tms.store.order.lack.amount=Order freight amount is missing
tms.store.order.pkg.comp.amount.invalid=Compensation amount must be > 0
tms.store.order.pkg.beyond.amount=Compensation amount exceeds freight
tms.store.order.pkg.no.balance.account=Customer balance account does not exist
tms.store.order.pkg.beyond.balance=Insufficient customer balance
tms.store.order.pkg.compensate.failed=Compensation failed
tms.store.order.pkg.pay.save.failed=Save compensation record failed
tms.store.order.status.invalid=The current order status does not allow compensation
tms.store.order.already.writeoff=This order has been cancelled, please do not repeat the operation
tms.store.phone.already.exists=The contact phone number already exists, please re-enter

error.warehouse.notExist=The selected warehouse location does not exist!
error.order.alreadyScanned=Order [{0}] already has an on-shelf scanning record!
error.order.noNeedScan=Order [{0}] does not require return warehouse scanning!
error.order.notFound=No corresponding order found!
error.submit.fail=Submission failed!
error.orderNo.format=Order number format error, please check!
error.order.alreadyOnShelf=This package order already has an on-shelf record!
error.order.notInAppointment=The order is not in the appointment state and cannot be processed for outbound.


//导入提示语
order.upload.valid.file=Please upload a valid Excel file!
order.upload.empty.data=The Excel file contains no data.

tms.ServiceRegionDetail.note.Excel.file.processing.success=The Excel file for the detailed configuration of district postal codes has been processed successfully. A total of {0} records have been processed.
tms.ServiceRegionDetail.file.processing.errors=The import processing of the zoning postal code configuration details has failed. Please check the information：{0}
tms.ServiceRegionDetail.Excel.file.processing.exception=Error occurred while processing the Excel file for the detailed configuration of district postal codes: {0}

tms.ServiceQuotePrice.note.Excel.file.processing.success=The Excel file for price configuration details has been processed successfully. A total of {0} records have been processed.
tms.ServiceQuotePrice.file.processing.errors=The import process of the price configuration details has failed. Please check the information: {0}
tms.ServiceQuotePrice.Excel.file.processing.exception=Error occurred while processing the Excel file containing price configuration details: {0}


tms.customer.reservation.pickup.address.errors = Please fill in the correct Canadian address or postal code
tms.customer.reservation.pickup.account.errors = The customer's account information is abnormal. Please contact the developer for inspection!

//服务商管理
tms.serviceProvider.name.exists=The name or code of the service provider cannot be duplicated：{0}

//-服务商邮编
tms.serviceProvider.postalCode.exists=The name or code of the zone cannot be duplicated：{0}
tms.ServiceRegionDetail.file.too.many.rows=The number of lines in the uploaded file exceeds the limit. The maximum allowed is {0} records


//-新增服务商邮编配置子表
tms.serviceProvider.postalCode.dest.exists=The destination postal code range cannot have the same starting and ending numbers:{0}
tms.serviceProvider.postalCode.shipper.exists=The starting and ending range of the sender's postal code for each postal code zone cannot be the same:{0}

//--------------------------------财务模块
tms.serviceQuote.name.exists=The name or code of the quotation cannot be duplicated:{0}
tms.serviceQuote.channel.name.exists=The corresponding quotation rule for this service provider-channel already exists
tms.serviceQuote.keda.fenqu.exists=The corresponding quotation rule for this reachable partition already exists

# 快递业务模块国际-en
# general
tms.store.params.exception=Parameter exception.
tms.store.code.invalid=Verification code invalid.
tms.store.phone.illegal=The phone number is illegal.
tms.store.phone.exists=The phone number already exists.
# order
tms.store.order.not.exists=Order does not exist.
tms.store.order.goods.not.exists=The cargo information cannot be empty.
tms.store.order.goods.check.not.empty=The length/width/height/weight of the goods cannot be empty.
tms.store.order.provider.not.exists=The end delivery service provider cannot be empty.
tms.store.order.freight.amount.exception=Abnormal freight charges.
tms.store.order.total.weight.kg.maxLimit=The weight cannot exceed 68kg.
tms.store.order.totalWeight.kg.maxLimit=The total weight cannot exceed 68kg.
tms.store.order.total.weight.lb.maxLimit=The weight cannot exceed 149.91 pounds.
tms.store.order.write.off.finished=The order has been cancelled and cannot be operated.
tms.store.order.print.script.failed=Printing voucher failed.
tms.store.order.cancel.order.status.failed=Abnormal order status, so canceling the order is not supported.
tms.store.order.not.awaiting.shipment.write.off.failed=The order status is not pending shipment and does not support verification.
tms.store.order.write.off.finished.failed=The order has been written off and does not support verification operations.
tms.store.order.write.off.freight.amount.exception=Offline payment, the freight amount must be greater than or equal to zero.
tms.store.order.write.off.pay.type.exception=Abnormal payment method.
# customer
tms.store.customer.not.exists=The customer does not exist.
tms.store.customer.email.not.legal=Illegal email address.
tms.store.customer.email.exists=Email already exists.
tms.store.customer.email.not.match=Email mismatch.
tms.store.customer.email.modify.failed=Failed to modify email address.
tms.store.customer.phone.modify.failed=Failed to modify phone.
tms.store.customer.password.check.failed=The password entered twice is inconsistent.
tms.store.customer.reset.password.failed=Password reset failed.
tms.store.nb.customer.not.exists=NB system express delivery service customer does not exist.
# balance
tms.store.balance.customer.not.exists=Customer balance does not exist.
tms.store.balance.customer.not.enough=Insufficient balance, please go to recharge.
tms.store.balance.amount.change.failed=Balance change failed.
tms.store.balance.insufficient=Insufficient customer balance.
# provider
tms.store.provider.not.exists=No service provider enabled, inquiry not supported.
tms.store.provider.box.not.exists=Blind box not configured, does not support inquiry.
tms.store.provider.default.dispatch.not.supported=There are no recommended service providers available for delivery. Please verify the address and cargo information.
tms.store.provider.other.dispatch.not.supported=There are no other service providers available for delivery. Please verify the address and cargo information.
# pushOrder
tms.store.push.order.nb.exception=Push NB system, push failed.
tms.store.push.order.package.exception=Push Package system, push failed.
# creditCard
tms.store.credit.card.exists=Card number already exists.
tms.store.credit.card.not.exists=Credit card information does not exist.
#promotion
tms.store.promotion.promoter.exists=The promoter or contact information already exists
tms.store.promotion.settle.amount.invalid=The settlement amount must be greater than 0
tms.store.promotion.commission.data.not.found=The corresponding commission data was not found
tms.store.promotion.settle.amount.exceed=The settlement amount cannot be greater than the unsettled amount
tms.store.promotion.data.validation.failed=Data validation failed. Please contact the administrator.
tms.store.promotion.promoter.order.exists=This promoter has promotion records and deletion is not allowed
tms.store.promotion.code.used=The discount code has been used and cannot be deleted
tms.store.promotion.code.valid.time.less=The discount code validity period cannot be less than 1 day


//--------------------------------客户渠道报价
tms.customerChannelQuote.name.exists=The quotation for this customer - this channel already exists

# 服务商利润配置
tms.store.profit.config.data.empty=The configuration object cannot be empty
tms.store.profit.config.detail.data.empty=The configuration weight details cannot be empty
tms.store.profit.config.add.error=Save failed: Exception occurred while inserting the main configuration
tms.store.profit.config.detail.add.error=Save failed: Exception occurred while inserting weight details configuration

tms.store.profit.config.update.data.empty=The configuration object or ID cannot be empty
tms.store.profit.config.update.error=Update failed: Main configuration update failed
tms.store.profit.config.detail.update.error=Update failed: Exception occurred while updating the weight details configuration
tms.store.profit.config.detail.update.add.error=Update failed: There is an abnormality in the configuration of the weight details

tms.store.profit.config.detail.weight.range.overlap=Weight ranges overlap or repeat: {0}
tms.store.profit.config.detail.weight.empty=The weight range cannot be left blank
tms.store.profit.config.detail.weight.start.end.ge=The starting weight must not exceed the ending weight：{0}

tms.store.profit.config.unique=The service provider already has an enabled profit allocation of {0}. You cannot add it again



# finance
tms.store.finance.order.number=OrderNumber
tms.store.finance.tracking.number=TrackingNumber
tms.store.finance.price=Price
tms.store.finance.pay.type=PayType
tms.store.finance.order.create.time=OrderCreateTime
tms.store.finance.name=Name
tms.store.finance.level=Level
tms.store.finance.type=Type

# channel
tms.channel.channel.code=Channel code
tms.channel.channel.name=Channel name
tms.channel.channel.type=Channel type
tms.channel.goods.type=Goods type
tms.channel.time.lineless=Time lineless
tms.channel.volume.weight=Volume weight
tms.channel.billing.node=Billing node
tms.channel.service.provider=Service provider
tms.channel.reachable.area=Reachable area
tms.channel.unreachable.area=Unreachable area