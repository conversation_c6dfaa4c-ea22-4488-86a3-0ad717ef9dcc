package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.tms.dto.TmsStoreProfitConfigDTO;
import com.jygjexp.jynx.tms.entity.TmsServiceProviderEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigEntity;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsServiceProviderMapper;
import com.jygjexp.jynx.tms.mapper.TmsStoreProfitConfigMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStoreProfitConfigService;
import com.jygjexp.jynx.tms.vo.TmsStoreProfitConfigPageVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigDetailEntity;
import com.jygjexp.jynx.tms.mapper.TmsStoreProfitConfigDetailMapper;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 服务商利润配置
 *
 * <AUTHOR>
 * @date 2025-08-26 10:46:10
 */
@Service
@RequiredArgsConstructor
public class TmsStoreProfitConfigServiceImpl extends ServiceImpl<TmsStoreProfitConfigMapper, TmsStoreProfitConfigEntity> implements TmsStoreProfitConfigService {
  private final TmsStoreProfitConfigDetailMapper tmsStoreProfitConfigDetailMapper;
  private final TmsStoreProfitConfigMapper tmsstoreProfitConfigMapper;
  private final TmsServiceProviderMapper tmsServiceProviderMapper;

    // 分页查询
/*    @Override
    public Page<TmsStoreProfitConfigEntity> search(Page page, TmsStoreProfitConfigPageVo vo) {
        MPJLambdaWrapper<TmsStoreProfitConfigEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsStoreProfitConfigEntity.class)
                .select(TmsServiceProviderEntity::getProviderCode)
                .select(TmsServiceProviderEntity::getProviderName)
                .leftJoin(TmsServiceProviderEntity.class, TmsServiceProviderEntity::getProviderId, TmsStoreProfitConfigEntity::getProviderId)
                .leftJoin(TmsStoreProfitConfigDetailEntity.class, TmsStoreProfitConfigDetailEntity::getProfitId, TmsStoreProfitConfigEntity::getId)

                .eq(ObjectUtil.isNotNull(vo.getProviderId()), TmsStoreProfitConfigEntity::getProviderId, vo.getProviderId())
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), TmsStoreProfitConfigEntity::getIsValid, vo.getIsValid())
                .eq(ObjectUtil.isNotNull(vo.getRuleType()), TmsStoreProfitConfigEntity::getRuleType, vo.getRuleType())
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()), TmsStoreProfitConfigEntity::getCreateTime,
                        vo.getStartTime(), vo.getEndTime())
                .orderByDesc(TmsStoreProfitConfigEntity::getCreateTime);
        return tmsstoreProfitConfigMapper.selectJoinPage(page, TmsStoreProfitConfigDTO.class, wrapper);
    }*/

    @Override
    public Page<TmsStoreProfitConfigEntity> search(Page page, TmsStoreProfitConfigPageVo vo) {
        MPJLambdaWrapper<TmsStoreProfitConfigEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsStoreProfitConfigEntity.class)
                .select(TmsServiceProviderEntity::getProviderCode)
                .select(TmsServiceProviderEntity::getProviderName)
                // 聚合子表最小/最大重量 + 单位
                .select("CONCAT(MIN(t2.start_weight), '-', MAX(t2.end_weight)) AS weightRange")
                .leftJoin(TmsServiceProviderEntity.class, TmsServiceProviderEntity::getProviderId, TmsStoreProfitConfigEntity::getProviderId)
                .leftJoin(TmsStoreProfitConfigDetailEntity.class, TmsStoreProfitConfigDetailEntity::getProfitId, TmsStoreProfitConfigEntity::getId)

                .eq(ObjectUtil.isNotNull(vo.getProviderId()), TmsStoreProfitConfigEntity::getProviderId, vo.getProviderId())
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), TmsStoreProfitConfigEntity::getIsValid, vo.getIsValid())
                .eq(ObjectUtil.isNotNull(vo.getRuleType()), TmsStoreProfitConfigEntity::getRuleType, vo.getRuleType())
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()),
                        TmsStoreProfitConfigEntity::getCreateTime, vo.getStartTime(), vo.getEndTime())
                .groupBy(TmsStoreProfitConfigEntity::getId)
                .orderByDesc(TmsStoreProfitConfigEntity::getCreateTime);

        return tmsstoreProfitConfigMapper.selectJoinPage(page, TmsStoreProfitConfigDTO.class, wrapper);
    }


    //新增服务商利润配置
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDeep(TmsStoreProfitConfigEntity tmsStoreProfitConfig) {
        if (Objects.isNull(tmsStoreProfitConfig)) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.data.empty",null));
        }

        List<TmsStoreProfitConfigDetailEntity> details = tmsStoreProfitConfig.getTmsStoreProfitConfigDetailList();
        // 校验服务商唯一性
        validateUniqueProfitConfig(tmsStoreProfitConfig);
        validateWeightConfigs(details); // 新增前校验

        int insert = baseMapper.insert(tmsStoreProfitConfig);
        if (insert <= 0) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.add.error",null));
        }

        for (TmsStoreProfitConfigDetailEntity detail : tmsStoreProfitConfig.getTmsStoreProfitConfigDetailList()) {
            if (Objects.isNull(detail)) {
                continue;
            }
            detail.setProfitId(tmsStoreProfitConfig.getId());
            int row = tmsStoreProfitConfigDetailMapper.insert(detail);
            if (row <= 0) {
                throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.detail.add.error",null));
            }
        }

        return Boolean.TRUE;
    }

    // 修改服务商利润配置
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDeep(TmsStoreProfitConfigEntity tmsStoreProfitConfig) {
        if (Objects.isNull(tmsStoreProfitConfig) || Objects.isNull(tmsStoreProfitConfig.getId())) {
            throw new CustomBusinessException(
                    LocalizedR.getMessage("tms.store.profit.config.update.data.empty", null)
            );
        }

        List<TmsStoreProfitConfigDetailEntity> details = tmsStoreProfitConfig.getTmsStoreProfitConfigDetailList();

        // 校验服务商唯一性
        validateUniqueProfitConfig(tmsStoreProfitConfig);

        // 校验重量区间（如果不为空）
        if (CollUtil.isNotEmpty(details)) {
            validateWeightConfigs(details);
        }

        // 更新主表
        int update = baseMapper.updateById(tmsStoreProfitConfig);
        if (update <= 0) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.update.error", null));
        }

        // 如果明细不为空，则先删除旧明细，再批量插入新的明细(覆盖)
        if (CollUtil.isNotEmpty(details)) {
            // 删除旧明细
            tmsStoreProfitConfigDetailMapper.delete(Wrappers.<TmsStoreProfitConfigDetailEntity>lambdaQuery()
                            .eq(TmsStoreProfitConfigDetailEntity::getProfitId, tmsStoreProfitConfig.getId()));

            // 批量插入新的明细
            for (TmsStoreProfitConfigDetailEntity detail : details) {
                detail.setProfitId(tmsStoreProfitConfig.getId());
                int row = tmsStoreProfitConfigDetailMapper.insert(detail);
                if (row <= 0) {
                    throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.detail.update.add.error", null));
                }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public TmsStoreProfitConfigDTO getByIdDeatil(Long id) {
        // 查询主表信息
        TmsStoreProfitConfigEntity config = this.getById(id);
        if (config == null) {
            return null;
        }
        // 查询并填充明细
        List<TmsStoreProfitConfigDetailEntity> details = tmsStoreProfitConfigDetailMapper
                .selectList(new LambdaQueryWrapper<TmsStoreProfitConfigDetailEntity>().eq(TmsStoreProfitConfigDetailEntity::getProfitId, id));
        config.setTmsStoreProfitConfigDetailList(details);

        TmsStoreProfitConfigDTO dto = new TmsStoreProfitConfigDTO();
        BeanUtils.copyProperties(config, dto);

        // 查询服务商信息
        TmsServiceProviderEntity provider = tmsServiceProviderMapper.selectById(config.getProviderId());
        dto.setProviderName(provider!=null?provider.getProviderName():"");
        dto.setProviderCode(provider!=null?provider.getProviderCode():"");

        return dto;
    }


    // 通过id删除服务商利润配置
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeDeep(Long[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            throw new CustomBusinessException("ID数组不能为空");
        }
        int deleted = baseMapper.deleteBatchIds(CollUtil.toList(ids));
        if (deleted <= 0) {
            throw new CustomBusinessException("删除失败：主配置删除异常");
        }

        int childDeleted = tmsStoreProfitConfigDetailMapper.delete(
                Wrappers.<TmsStoreProfitConfigDetailEntity>lambdaQuery().in(TmsStoreProfitConfigDetailEntity::getProfitId, ids)
        );
        if (childDeleted < 0) {
            throw new CustomBusinessException("删除失败：重量明细配置删除异常");
        }

        return Boolean.TRUE;
    }

    //通过id删除服务商利润配置子表数据
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeChild(Long[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            throw new CustomBusinessException("删除失败：重量明细配置ID数组不能为空");
        }

        int deleted = tmsStoreProfitConfigDetailMapper.deleteBatchIds(CollUtil.toList(ids));
        if (deleted <= 0) {
            throw new CustomBusinessException("删除失败：重量明细配置删除异常");
        }

        return Boolean.TRUE;
    }

    /**
     * 校验重量区间配置
     *
     * @param details 配置明细
     */
    private void validateWeightConfigs(List<TmsStoreProfitConfigDetailEntity> details) {
        if (CollUtil.isEmpty(details)) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.detail.data.empty", null));
        }

        // 过滤空对象
        List<TmsStoreProfitConfigDetailEntity> validList = details.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 校验每条记录：起始 <= 结束
        for (TmsStoreProfitConfigDetailEntity d : validList) {
            if (d.getStartWeight() == null || d.getEndWeight() == null) {
                throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.detail.weight.empty",null));
            }
            if (d.getStartWeight().compareTo(d.getEndWeight()) > 0) {
                throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.detail.weight.start.end.ge",
                        new Object[]{d.getStartWeight() + "-" + d.getEndWeight()}));
            }
        }

        // 按起始重量排序
        validList.sort(Comparator.comparing(TmsStoreProfitConfigDetailEntity::getStartWeight));

        // 校验区间是否重叠
        for (int i = 0; i < validList.size(); i++) {
            TmsStoreProfitConfigDetailEntity a = validList.get(i);
            for (int j = i + 1; j < validList.size(); j++) {
                TmsStoreProfitConfigDetailEntity b = validList.get(j);

                // 判断区间是否重叠（包含/重复）
                boolean overlap = a.getEndWeight().compareTo(b.getStartWeight()) >= 0;
                if (overlap) {
                    throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.detail.weight.range.overlap",
                            new Object[]{"[" + a.getStartWeight() + "-" + a.getEndWeight() +
                            "] 与 [" + b.getStartWeight() + "-" + b.getEndWeight() + "]"}));
                }
            }
        }
    }



    /**
     * 校验一个服务商只能有两条已启用的利润配置
     * （一个盲盒开启，一个盲盒不开启）
     *
     * @param entity 主表配置
     */
    private void validateUniqueProfitConfig(TmsStoreProfitConfigEntity entity) {
        if (Objects.isNull(entity) || Objects.isNull(entity.getProviderId())) {
            throw new CustomBusinessException("服务商ID不能为空");
        }
        if (Objects.isNull(entity.getBlindBoxFlag())) {
            throw new CustomBusinessException("盲盒标识不能为空");
        }

        // 只校验启用的情况
            LambdaQueryWrapper<TmsStoreProfitConfigEntity> query = Wrappers.lambdaQuery();
            query.eq(TmsStoreProfitConfigEntity::getProviderId, entity.getProviderId())
                    .eq(TmsStoreProfitConfigEntity::getBlindBoxFlag, entity.getBlindBoxFlag())
                    .eq(TmsStoreProfitConfigEntity::getIsValid, 1);

            // 修改时排除自身
            if (entity.getId() != null) {
                query.ne(TmsStoreProfitConfigEntity::getId, entity.getId());
            }

            Long count = baseMapper.selectCount(query);
            if (count != null && count > 0) {
                throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.unique",
                        new Object[]{entity.getBlindBoxFlag() == 1 ? " [Open a blind box] " : " [box has not been opened] "}
                ));
            }
    }

    // 启用停用
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(Long id, Integer isValid) {
        if (id == null) {
            throw new CustomBusinessException("配置ID不能为空");
        }
        if (isValid == null || (!isValid.equals(0) && !isValid.equals(1))) {
            throw new CustomBusinessException("启用/停用状态不合法");
        }

        // 查询当前配置
        TmsStoreProfitConfigEntity config = baseMapper.selectById(id);
        if (config == null) {
            throw new CustomBusinessException("配置不存在");
        }

        if (isValid == 1) { // 启用
            // 查询同一服务商、同一盲盒状态、已启用的其他记录
            LambdaQueryWrapper<TmsStoreProfitConfigEntity> query = Wrappers.lambdaQuery();
            query.eq(TmsStoreProfitConfigEntity::getProviderId, config.getProviderId())
                    .eq(TmsStoreProfitConfigEntity::getBlindBoxFlag, config.getBlindBoxFlag())
                    .eq(TmsStoreProfitConfigEntity::getIsValid, 1)
                    .ne(TmsStoreProfitConfigEntity::getId, config.getId());

            Long count = baseMapper.selectCount(query);
            if (count != null && count > 0) {
                throw new CustomBusinessException(LocalizedR.getMessage("tms.store.profit.config.unique",
                        new Object[]{config.getBlindBoxFlag() == 1 ? "Open a blind box" : "box has not been opened"}
                ));
            }
        }

        // 更新状态
        config.setIsValid(isValid);
        int row = baseMapper.updateById(config);
        if (row <= 0) {
            throw new CustomBusinessException("更新启用/停用状态失败");
        }

        return Boolean.TRUE;
    }

}