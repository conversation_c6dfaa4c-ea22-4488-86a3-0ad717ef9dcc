package com.jygjexp.jynx.tms.service.impl;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsCargoInfoEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsManualSortingRecordEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.mapper.TmsCargoInfoMapper;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsManualSortingRecordMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsLmdAppService;
import com.jygjexp.jynx.tms.vo.app.TmsAppDriverSubOrderPodVo;
import com.jygjexp.jynx.tms.vo.app.TmsManualSortingUpdatePkg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 中大件司机app相关代码
 *
 * @author: xiongpengfei
 * @create: 2025/8/08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsLmdAppServiceImpl implements TmsLmdAppService {

    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsCargoInfoMapper cargoInfoMapper;
    private final TmsManualSortingRecordMapper manualSortingRecordMapper;

    //客户单号规则
    private static final String[] CUSTOMER_LABEL_PREFIXES = {"GV", "JY", "U9999"};


    /*
    *
    * 派送订单二次修改\编辑 Pod 送货凭证
    *
    */
    @Override
    public R appDeliveryUploadPickupProof(List<TmsAppDriverSubOrderPodVo> voList) {
        if (voList == null || voList.isEmpty()) {
            return R.failed("参数不能为空");
        }

        // 提取所有子单号
        List<String> subOrderNos = voList.stream()
                .map(TmsAppDriverSubOrderPodVo::getSubOrder)
                .collect(Collectors.toList());

        // 查询数据库中这些子单记录
        List<TmsCustomerOrderEntity> subOrdersList = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, subOrderNos)
        );

        if (CollUtil.isEmpty(subOrdersList)) {
            String orderNos = String.join(",", subOrderNos);
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", orderNos);
        }

        int updatedCount = 0;

        for (TmsAppDriverSubOrderPodVo podVo : voList) {
            // 找到对应的数据库记录
            TmsCustomerOrderEntity order = subOrdersList.stream()
                    .filter(o -> o.getEntrustedOrderNumber().equals(podVo.getSubOrder()))
                    .findFirst()
                    .orElse(null);

            if (order == null) {
                continue; // 不存在的跳过
            }

            // 校验订单状态（如果需要，只允许已完成的才更新）
        /*
        if (!NewOrderStatus.COMPLETED.getCode().equals(order.getOrderStatus())) {
            continue; // 不符合状态的跳过
        }
        */

            // 修改订单送货凭证（覆盖模式）
            order.setDeliveryProof(podVo.getDeliveryProof());
            int rows = customerOrderMapper.updateById(order);
            if (rows > 0) {
                updatedCount++;
            }
        }

        if (updatedCount == 0) {
            return R.failed("没有符合条件的子单被更新");
        }

        return R.ok("送货凭证已更新，共更新 " + updatedCount + " 条记录");
    }


    // App人工分拣-人工复核更新包裹信息
    @Override
    public R appManualSortingUpdate(TmsManualSortingUpdatePkg pkgVo) {
        try {
            // 参数校验
            if (pkgVo == null || StrUtil.isBlank(pkgVo.getOrderNo())) {
                return R.failed("参数不能为空");
            }

            String orderNo = pkgVo.getOrderNo().trim();
            if (orderNo.length() < 15) {
                return R.ok(Boolean.FALSE);
            }

            // -------- 计算当前子单体积 (m³) --------
            BigDecimal volume = BigDecimal.ZERO;
            if (pkgVo.getLength() != null && pkgVo.getWidth() != null && pkgVo.getHeight() != null) {
                try {
                    volume = pkgVo.getLength().multiply(pkgVo.getWidth()).multiply(pkgVo.getHeight())
                            .divide(BigDecimal.valueOf(1_000_000), 6, RoundingMode.HALF_UP);
                } catch (ArithmeticException ex) {
                    log.error("体积计算异常，订单号：{}", orderNo, ex);
                    return R.failed("体积计算异常");
                }
            }

            BigDecimal weight = pkgVo.getWeight();

            // -------- 动态使用 --------   isCustomerOrder：判断是否使用客户单号进行操作
            boolean isCustomerOrder = isCustomerOrder(orderNo);
            SFunction<TmsCustomerOrderEntity, ?> orderNoColumn = isCustomerOrder
                    ? TmsCustomerOrderEntity::getCustomerOrderNumber
                    : TmsCustomerOrderEntity::getEntrustedOrderNumber;

            SFunction<TmsCargoInfoEntity, ?> orderNoColumnTwo = isCustomerOrder
                    ? TmsCargoInfoEntity::getCustomerOrderNumber
                    : TmsCargoInfoEntity::getEntrustedOrderNumber;

            SFunction<TmsManualSortingRecordEntity, ?> orderNoColumnStoring = isCustomerOrder
                    ? TmsManualSortingRecordEntity::getCustomerOrderNumber
                    : TmsManualSortingRecordEntity::getEntrustedOrderNo;

            // -------- 更新当前子单 --------
            boolean updateResult = customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(orderNoColumn, orderNo)
                    .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE)
                    .set(TmsCustomerOrderEntity::getReviewVolume, volume)
                    .set(TmsCustomerOrderEntity::getReviewWeight, weight)) > 0;

            if (!updateResult) {
                return R.ok(Boolean.FALSE);
            }

            // -------- 更新当前子单货物信息 --------
            boolean updateCargoInfo = cargoInfoMapper.update(new LambdaUpdateWrapper<TmsCargoInfoEntity>()
                    .eq(orderNoColumnTwo, orderNo)
                    .set(TmsCargoInfoEntity::getReviewVolume, volume)
                    .set(TmsCargoInfoEntity::getReviewWeight, weight)) > 0;

            if (!updateCargoInfo) {
                return R.ok(Boolean.FALSE);
            }

            // -------- 更新当前子单人工分拣记录信息 --------
            boolean updateStoringInfo = manualSortingRecordMapper.update(new LambdaUpdateWrapper<TmsManualSortingRecordEntity>()
                    .eq(orderNoColumnStoring, orderNo)
                    .set(TmsManualSortingRecordEntity::getLength, pkgVo.getLength())
                    .set(TmsManualSortingRecordEntity::getWidth, pkgVo.getWidth())
                    .set(TmsManualSortingRecordEntity::getHeight, pkgVo.getHeight())
                    .set(TmsManualSortingRecordEntity::getVolume, volume)
                    .set(TmsManualSortingRecordEntity::getWeight, weight)) > 0;

            if (!updateStoringInfo) {
                return R.ok(Boolean.FALSE);
            }


            // -------- 查询所有子单 --------
            LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE);

            // -------- 获取主单号 --------
            String mainOrderNo;
            if (isCustomerOrder) {
                // 如果是客户单号（不需要截取）
                mainOrderNo = orderNo;
                // 客户单号 → 精确匹配主单号
                wrapper.eq(orderNoColumn, mainOrderNo);
            } else {
                // 如果是委托单号，截取前length-3位作为主单号
                mainOrderNo = orderNo.substring(0, orderNo.length() - 3);
                // 委托单号 → 前缀匹配
                wrapper.likeRight(orderNoColumn, mainOrderNo);
            }

            List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(wrapper);
            if (CollUtil.isEmpty(subOrders)) {
                return R.ok(Boolean.FALSE);
            }

            // -------- 汇总子单体积和重量 --------
            BigDecimal totalVolume = subOrders.stream()
                    .map(TmsCustomerOrderEntity::getReviewVolume)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalWeight = subOrders.stream()
                    .map(TmsCustomerOrderEntity::getReviewWeight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // -------- 更新主单 --------
            int update = customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(orderNoColumn, mainOrderNo)
                    .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                    .set(TmsCustomerOrderEntity::getReviewVolume, totalVolume)
                    .set(TmsCustomerOrderEntity::getReviewWeight, totalWeight));

            return R.ok(update > 0);

        } catch (Exception e) {
            log.error("人工分拣复核更新失败, 参数: {}", pkgVo, e);
            return R.failed("人工分拣复核更新失败: " + e.getMessage());
        }
    }



    // 判断是否使用客户单号进行操作
    private boolean isCustomerOrder(String orderNumber) {
        if (orderNumber == null) return false;
        for (String start : CUSTOMER_LABEL_PREFIXES) {
            if (orderNumber.startsWith(start)) {
                return true;
            }
        }
        return false;
    }



}
