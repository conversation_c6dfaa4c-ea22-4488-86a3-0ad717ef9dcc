package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.tms.dto.TmsStorePromotionCodeDTO;
import com.jygjexp.jynx.tms.dto.TmsStorePromotionCodeDiscountDTO;
import com.jygjexp.jynx.tms.dto.TmsStorePromotionCodeQueryDTO;
import com.jygjexp.jynx.tms.entity.TmsStorePromoterEntity;
import com.jygjexp.jynx.tms.entity.TmsStorePromotionCodeDiscountEntity;
import com.jygjexp.jynx.tms.entity.TmsStorePromotionCodeEntity;
import com.jygjexp.jynx.tms.entity.TmsStorePromotionOrderEntity;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsStorePromotionCodeMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStorePromotionCodeDiscountService;
import com.jygjexp.jynx.tms.service.TmsStorePromotionCodeService;
import com.jygjexp.jynx.tms.service.TmsStorePromotionOrderService;
import com.jygjexp.jynx.tms.utils.LineValidator;
import com.jygjexp.jynx.tms.utils.SelectUtil;
import com.jygjexp.jynx.tms.vo.TmsStorePromotionCodeDetailVo;
import com.jygjexp.jynx.tms.vo.TmsStorePromotionCodeExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStorePromotionCodePageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 推广码表
 *
 * <AUTHOR>
 * @date 2025-08-13 15:54:07
 */
@Service
@RequiredArgsConstructor
public class TmsStorePromotionCodeServiceImpl extends ServiceImpl<TmsStorePromotionCodeMapper, TmsStorePromotionCodeEntity> implements TmsStorePromotionCodeService {
    private final TmsStorePromotionCodeDiscountService promotionCodeDiscountService;
    private final TmsStorePromotionOrderService promotionOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePromotionCode(TmsStorePromotionCodeDTO tmsStorePromotionCode) {
        // 校验推广码是否符合格式要求以及是否存在
        validatePromotionCode(tmsStorePromotionCode);
        // 校验优惠码有效期是否小于1天
        if (tmsStorePromotionCode.getValidEndTime().isBefore(tmsStorePromotionCode.getValidStartTime().plusDays(1))) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.promotion.code.valid.time.less", null));
        }

        // 保存推广码
        TmsStorePromotionCodeEntity tmsStorePromotionCodeEntity = new TmsStorePromotionCodeEntity();
        BeanUtil.copyProperties(tmsStorePromotionCode, tmsStorePromotionCodeEntity);
        // 处理服务商ID
        tmsStorePromotionCodeEntity.setProviderIds(
                Optional.ofNullable(tmsStorePromotionCode.getProviderIds())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(","))
        );
        tmsStorePromotionCodeEntity.setOperator(SelectUtil.getUserName());
        save(tmsStorePromotionCodeEntity);
        // 保存推广码关系
        promotionCodeDiscountService.saveBatch(tmsStorePromotionCode.getDiscounts().stream()
                .map(discount -> {
                    TmsStorePromotionCodeDiscountEntity tmsStorePromotionCodeDiscount = new TmsStorePromotionCodeDiscountEntity();
                    BeanUtil.copyProperties(discount, tmsStorePromotionCodeDiscount);
                    tmsStorePromotionCodeDiscount.setPromotionCodeId(tmsStorePromotionCodeEntity.getId());
                    return tmsStorePromotionCodeDiscount;
                }).collect(Collectors.toList()));
        return true;
    }

    @Override
    public TmsStorePromotionCodeDetailVo getDetailById(Long id) {
        // 先获取推广码信息
        TmsStorePromotionCodeEntity tmsStorePromotionCode = getById(id);
        if (tmsStorePromotionCode == null) {
            return new TmsStorePromotionCodeDetailVo();
        }
        TmsStorePromotionCodeDetailVo tmsStorePromotionCodeDetailVo = new TmsStorePromotionCodeDetailVo();
        BeanUtil.copyProperties(tmsStorePromotionCode, tmsStorePromotionCodeDetailVo);
        tmsStorePromotionCodeDetailVo.setValidStatus(tmsStorePromotionCode.getValidEndTime().isBefore(LocalDateTime.now()) ? 0 : 1);
        // 获取推广码折扣信息
        List<TmsStorePromotionCodeDiscountEntity> promotionCodeDiscountList =
                promotionCodeDiscountService.list(new LambdaQueryWrapper<TmsStorePromotionCodeDiscountEntity>()
                        .eq(TmsStorePromotionCodeDiscountEntity::getPromotionCodeId, id));
        tmsStorePromotionCodeDetailVo.setDiscounts(promotionCodeDiscountList.stream()
                .map(promotionCodeDiscount -> {
                    TmsStorePromotionCodeDiscountDTO tmsStorePromotionCodeDiscountDTO = new TmsStorePromotionCodeDiscountDTO();
                    BeanUtil.copyProperties(promotionCodeDiscount, tmsStorePromotionCodeDiscountDTO);
                    return tmsStorePromotionCodeDiscountDTO;
                }).collect(Collectors.toList())
        );

        return tmsStorePromotionCodeDetailVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePromotionCode(TmsStorePromotionCodeDTO tmsStorePromotionCode) {
        // 校验推广码是否符合格式要求以及是否存在
        validatePromotionCode(tmsStorePromotionCode);
        TmsStorePromotionCodeEntity tmsStorePromotionCodeEntity = new TmsStorePromotionCodeEntity();
        BeanUtil.copyProperties(tmsStorePromotionCode, tmsStorePromotionCodeEntity);
        // 处理服务商ID
        tmsStorePromotionCodeEntity.setProviderIds(
                Optional.ofNullable(tmsStorePromotionCode.getProviderIds())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(","))
        );
        tmsStorePromotionCodeEntity.setOperator(SelectUtil.getUserName());
        updateById(tmsStorePromotionCodeEntity);
        // 删除推广码关系
        promotionCodeDiscountService.remove(new LambdaQueryWrapper<TmsStorePromotionCodeDiscountEntity>()
                .eq(TmsStorePromotionCodeDiscountEntity::getPromotionCodeId, tmsStorePromotionCode.getId()));
        // 添加推广码关系
        List<TmsStorePromotionCodeDiscountEntity> promotionCodeDiscountList = tmsStorePromotionCode.getDiscounts().stream()
                .map(discount -> {
                    TmsStorePromotionCodeDiscountEntity tmsStorePromotionCodeDiscount = new TmsStorePromotionCodeDiscountEntity();
                    BeanUtil.copyProperties(discount, tmsStorePromotionCodeDiscount);
                    tmsStorePromotionCodeDiscount.setId(null);
                    tmsStorePromotionCodeDiscount.setPromotionCodeId(tmsStorePromotionCodeEntity.getId());
                    return tmsStorePromotionCodeDiscount;
                }).collect(Collectors.toList());
        promotionCodeDiscountService.saveBatch(promotionCodeDiscountList);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removePromotionCode(Long id) {
        // 判断是否存在推广数据，若存在则不允许删除
        List<TmsStorePromotionOrderEntity> list = promotionOrderService.list(new LambdaQueryWrapper<TmsStorePromotionOrderEntity>()
                .eq(TmsStorePromotionOrderEntity::getPromotionCodeId, id));
        if (null != list && list.size() > 0) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.promotion.code.used", null));
        }
        // 先删除推广码数据
        removeById(id);
        // 再删除折扣相关数据
        promotionCodeDiscountService.remove(new LambdaQueryWrapper<TmsStorePromotionCodeDiscountEntity>()
                .eq(TmsStorePromotionCodeDiscountEntity::getPromotionCodeId, id));
        return true;
    }

    @Override
    public List<TmsStorePromotionCodeExcelVo> export(TmsStorePromotionCodeQueryDTO tmsStorePromotionCode) {
        LambdaQueryWrapper<TmsStorePromotionCodeEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq((tmsStorePromotionCode.getPromoterId() != null), TmsStorePromotionCodeEntity::getPromoterId, tmsStorePromotionCode.getPromoterId())
                .like((StrUtil.isNotBlank(tmsStorePromotionCode.getCode())), TmsStorePromotionCodeEntity::getCode, tmsStorePromotionCode.getCode())
                .between((tmsStorePromotionCode.getCreateStartTime() != null && tmsStorePromotionCode.getCreateEndTime() != null),
                        TmsStorePromotionCodeEntity::getCreateTime, tmsStorePromotionCode.getCreateStartTime(), tmsStorePromotionCode.getCreateEndTime())
                .between((tmsStorePromotionCode.getValidStartTime() != null && tmsStorePromotionCode.getValidEndTime() != null),
                        TmsStorePromotionCodeEntity::getValidStartTime, tmsStorePromotionCode.getValidStartTime(), tmsStorePromotionCode.getValidEndTime())
                .orderByDesc(TmsStorePromotionCodeEntity::getCreateTime);
        List<TmsStorePromotionCodeEntity> promotionCodeEntities = list(wrapper);

        if (CollUtil.isEmpty(promotionCodeEntities)) {
            return Collections.emptyList();
        }

        return promotionCodeEntities.stream()
                .map(item -> {
                    TmsStorePromotionCodeExcelVo excelVo = new TmsStorePromotionCodeExcelVo();
                    BeanUtils.copyProperties(item, excelVo);
                    excelVo.setStatus(item.getStatus().equals(0) ? "启用" : "禁用");
                    excelVo.setValidTimeStatus(item.getValidEndTime().isBefore(LocalDateTime.now()) ? "可用" : "不可用");
                    excelVo.setValidTime(item.getValidStartTime() + " - " + item.getValidEndTime());
                    return excelVo;
                }).collect(Collectors.toList());
    }

    @Override
    public IPage<TmsStorePromotionCodePageVo> promotionCodePage(Page page, TmsStorePromotionCodeQueryDTO tmsStorePromotionCode) {
        MPJLambdaWrapper<TmsStorePromotionCodeEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsStorePromotionCodeEntity.class)
                .select(TmsStorePromoterEntity::getPromoterName)
                .leftJoin(TmsStorePromoterEntity.class, TmsStorePromoterEntity::getId, TmsStorePromotionCodeEntity::getPromoterId)
                .eq((tmsStorePromotionCode.getPromoterId() != null), TmsStorePromotionCodeEntity::getPromoterId, tmsStorePromotionCode.getPromoterId())
                .eq((tmsStorePromotionCode.getStatus() != null), TmsStorePromotionCodeEntity::getStatus, tmsStorePromotionCode.getStatus())
                .like((StrUtil.isNotBlank(tmsStorePromotionCode.getCode())), TmsStorePromotionCodeEntity::getCode, tmsStorePromotionCode.getCode())
                .between((tmsStorePromotionCode.getCreateStartTime() != null && tmsStorePromotionCode.getCreateEndTime() != null),
                        TmsStorePromotionCodeEntity::getCreateTime, tmsStorePromotionCode.getCreateStartTime(), tmsStorePromotionCode.getCreateEndTime())
                .between((tmsStorePromotionCode.getValidStartTime() != null && tmsStorePromotionCode.getValidEndTime() != null),
                        TmsStorePromotionCodeEntity::getValidStartTime, tmsStorePromotionCode.getValidStartTime(), tmsStorePromotionCode.getValidEndTime())
                .orderByDesc(TmsStorePromotionCodeEntity::getCreateTime);
        Page<TmsStorePromotionCodePageVo> resultPage = baseMapper.selectJoinPage(page, TmsStorePromotionCodePageVo.class, wrapper);
        resultPage.getRecords().forEach(item -> {
            item.setValidStatus(item.getValidEndTime().isBefore(LocalDateTime.now()) ? 0 : 1);
        });
        return resultPage;
    }

//    @Override
//    public void checkPromotionCodeValidStatus() {
//        // 获取所有可用的推广码
//        List<TmsStorePromotionCodeEntity> promotionCodeEntities = list(new LambdaQueryWrapper<TmsStorePromotionCodeEntity>()
//                .eq(TmsStorePromotionCodeEntity::getValidStatus, 1));
//        // 判断是否存在过期的推广码
//        List<TmsStorePromotionCodeEntity> promotionCodeNoValid = promotionCodeEntities.stream()
//                .filter(item -> item.getValidEndTime().isBefore(LocalDateTime.now()))
//                .collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(promotionCodeNoValid)) {
//            // 禁用推广码
//            update(new LambdaUpdateWrapper<TmsStorePromotionCodeEntity>()
//                    .set(TmsStorePromotionCodeEntity::getValidStatus, 0)
//                    .in(TmsStorePromotionCodeEntity::getId, promotionCodeNoValid.stream().map(TmsStorePromotionCodeEntity::getId).collect(Collectors.toList())));
//        }
//    }

    /**
     * 校验推广码
     */
    private void validatePromotionCode(TmsStorePromotionCodeDTO tmsStorePromotionCode) {
        if (tmsStorePromotionCode.getCode() != null && !LineValidator.isAlphaNumericOnly(tmsStorePromotionCode.getCode())) {
            throw new CustomBusinessException("推广码只能由数字和字母构成！");
        } else if (tmsStorePromotionCode.getCode() != null && tmsStorePromotionCode.getCode().length() != 10) {
            throw new CustomBusinessException("推广码长度只能为10位！");
        } else if (tmsStorePromotionCode.getCode() != null &&
                this.count(new LambdaQueryWrapper<TmsStorePromotionCodeEntity>()
                        .eq(TmsStorePromotionCodeEntity::getCode, tmsStorePromotionCode.getCode())
                        .ne((tmsStorePromotionCode.getPromoterId() != null), TmsStorePromotionCodeEntity::getPromoterId, tmsStorePromotionCode.getPromoterId())) > 0) {
            throw new CustomBusinessException("推广码已存在！");
        }
    }
}