package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.feign.annotation.NoToken;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.annotation.ReCaptchaCheck;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.utils.PdfMergeUtil;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsCustomerOrderExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsCustomerZdjOrderExcelVo;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 中大件客户订单
 *
 * <AUTHOR>
 * @date 2025-03-13 15:09:24
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCustomerOrder" )
@Tag(description = "tmsCustomerOrder" , name = "中大件客户订单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCustomerOrderController {

    private final  TmsCustomerOrderService tmsCustomerOrderService;

    /**
     * 客户订单分页查询
     * @param page 分页对象
     * @param orderPageVo 中大件客户订单
     * @return
     */
    @Operation(summary = "卡派客户订单分页查询" , description = "卡派客户订单分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_view')" )
    public R getTmsCustomerOrderPage(@ParameterObject Page page, @ParameterObject TmsCustomerOrderPageVo orderPageVo) {
        return R.ok(tmsCustomerOrderService.search(page, orderPageVo,true));
    }



//     /**
//     * 客户订单分页查询
//     * @param page 分页对象
//     * @param orderPageVo 中大件客户订单
//     * @return
//     */
//    @Operation(summary = "中大件客户订单分页查询" , description = "中大件客户订单分页查询" )
//    @PostMapping("/getZdjPage" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_view')" )
//    public R getTmsCustomerOrderZdjPage(@ParameterObject Page page, @ParameterObject TmsCustomerOrderPageVo orderPageVo) {
//        return R.ok(tmsCustomerOrderService.search(page, orderPageVo, false));
//    }


     /**
     * 客户订单分页查询
     */
    @Operation(summary = "中大件客户订单分页查询" , description = "中大件客户订单分页查询" )
    @PostMapping("/getZdjPage" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_view')" )
    public R getTmsCustomerOrderZdjPage(@RequestBody TmsCustomerOrderPageRequest requestVo) {
        return R.ok(tmsCustomerOrderService.search(new Page(requestVo.getCurrent(), requestVo.getSize()), requestVo.getRequestVo(), false));
    }


//    /**
//     * 通过id查询中大件客户订单
//     * @param id id
//     * @return R
//     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
//    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_view')" )
//    public R getById(@PathVariable("id" ) Long id) {
//        return R.ok(tmsCustomerOrderService.getById(id));
//    }


    /**
     * 通过id查询中大件客户订单
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCustomerOrderService.getDetail(id));
    }

    /**
     * 新增中大件客户订单
     * @param tmsCustomerOrder 中大件客户订单
     * @return R
     */
    @Operation(summary = "新增中大件客户订单" , description = "新增中大件客户订单" )
    @SysLog("新增中大件客户订单" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R save(@RequestBody TmsCustomerOrderEntity tmsCustomerOrder) {
        return R.ok(tmsCustomerOrderService.create(tmsCustomerOrder));
    }

    /**
     * 修改中大件客户订单
     * @param tmsCustomerOrder 中大件客户订单
     * @return R
     */
    @Operation(summary = "修改中大件客户订单" , description = "修改中大件客户订单" )
    @SysLog("修改中大件客户订单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_edit')" )
    public R updateById(@RequestBody TmsCustomerOrderEntity tmsCustomerOrder) {
        if (tmsCustomerOrder.getReceiveType()!=null){
            return R.ok(tmsCustomerOrderService.updateCustomerOrderZdj(tmsCustomerOrder));
        }
        return R.ok(tmsCustomerOrderService.updateCustomerOrder(tmsCustomerOrder));
    }


    /**
     * 订单详情箱号查询轨迹
     *
     * @return R
     */
    @Operation(summary = "订单详情箱号查询轨迹", description = "订单详情箱号查询轨迹")
    @GetMapping("/box/getTrack")
    public R getBoxTrack(@RequestParam String orderNo) {
        return R.ok(tmsCustomerOrderService.getOrderTrackList(orderNo));
    }


    /**
     * 修改中大件客户订单
     * @return R
     */
    @Operation(summary = "修改中大件客户订单" , description = "修改中大件客户订单" )
    @SysLog("修改中大件客户订单" )
    @PutMapping("/updateZdjOrder")
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_edit')" )
    public R updateZdjOrder(@RequestBody TmsCustomerOrderEntity tmsCustomerOrder) {
        return R.ok(tmsCustomerOrderService.updateZdjCustomerOrder(tmsCustomerOrder));
    }

    /**
     * 通过id删除中大件客户订单
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除中大件客户订单" , description = "通过id删除中大件客户订单" )
    @SysLog("通过id删除中大件客户订单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return tmsCustomerOrderService.updateCustomerOrderByIds(ids);
    }


    /**
     * 卡派客户订单导出
     * @param vo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @SysLog("卡派客户订单导出")
    @Operation(summary = "卡派客户订单导出" , description = "卡派客户订单导出" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_export')" )
    public List<TmsCustomerOrderExcelVo> export(TmsCustomerOrderPageVo vo, Long[] ids) {
        return tmsCustomerOrderService.getExcel(vo, ids);
    }


    /**
     * 中大件客户订单导出
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/exportZdj")
    @Operation(summary = "中大件客户订单导出" , description = "中大件客户订单导出" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_export')" )
    public List<TmsCustomerZdjOrderExcelVo> exportZdj(@RequestBody TmsCustomerOrderPageRequest request) {
        return tmsCustomerOrderService.getZdjExcel(request.getRequestVo(), request.getIds());
    }

    /**
     * 中大件订单询价
     * @param vo id列表
     * @return R
     */
    @Operation(summary = "中大件订单询价" , description = "中大件订单询价" )
    @SysLog("中大件订单询价" )
    @PostMapping("/getOrderPrice")
    //@PreAuthorize("@pms.hasPermission('tms_tmsEntrustedOrder_orderPrice')" )
    public R getOrderPrice(@Valid @RequestBody TmsEntrustedOrderVo vo) {
        return tmsCustomerOrderService.getOrderPrice(vo);
    }



    /**
     * 新增换单
     * @param vo id列表
     * @return R
     */
    @Operation(summary = "新增换单" , description = "新增换单" )
    @SysLog("新增换单" )
    @PreAuthorize("@pms.hasPermission('tms_tmsThirdPartPost_add')" )
    @PostMapping("/saveNewLabel")
    public R saveNewLabel(@RequestBody ExchangeVo  vo) {
        return tmsCustomerOrderService.exchangeOrder(vo);
    }


    /**
     * 修改换单
     * @param vo id列表
     * @return R
     */
    @Operation(summary = "修改换单" , description = "修改换单" )
    @SysLog("修改换单" )
    @PreAuthorize("@pms.hasPermission('tms_tmsThirdPartPost_edit')" )
    @PostMapping("/updateNewLabel")
    public R updateNewLabel(@RequestBody ExchangeVo  vo) {
        return tmsCustomerOrderService.updateExchangeOrder(vo);
    }




    /**
     * 订单调度-分配承运商-同步生成委托订单
     * @param customerOrderNumber
     * @param carrierId
     * @return
     */
    @Operation(summary = "订单调度-分配承运商", description = "订单调度-分配承运商")
    @PostMapping("/assignCarrier")
    public R assignCarrier(@Parameter(description = "客户单号-多个单号用逗号分割", required = true) @RequestParam String customerOrderNumber,
                           @Parameter(description = "承运商ID", required = true) @RequestParam Long carrierId) {
        return R.ok(tmsCustomerOrderService.assignCarrier(customerOrderNumber, carrierId));
    }

    /**
     * 订单调度-判断承运商区域(经纬度)
     * @param vo
     * @return
     */
    @Operation(summary = "订单调度-判断承运商区域(经纬度)", description = "订单调度-判断承运商区域(经纬度)")
    @PostMapping("/isCarrierArea")
    public Boolean isCarrierArea(@RequestBody TmsIsCarrierAreaVo vo) {
        return tmsCustomerOrderService.isCarrierArea(vo);
    }

    /**
     * 订单调度-判断承运商区域(三字邮编)
     * @param vo
     * @return
     */
    @Operation(summary = "订单调度-判断承运商区域(三字邮编)", description = "订单调度-判断承运商区域(三字邮编)")
    @PostMapping("/isPostCarrierArea")
    public Boolean isPostCarrierArea(@RequestBody TmsIsCarrierAreaVo vo) {
        return tmsCustomerOrderService.isPostCarrierArea(vo);
    }

    @Operation(summary = "取消下单", description = "取消下单")
    @PostMapping("/{id}/cancelOrder")
    public R cancelOrder(@PathVariable("id") Long id) {
        return R.ok(tmsCustomerOrderService.cancelOrder(id));
    }


    // 客户端订单查询列表
    @Operation(summary = "客户端订单查询列表", description = "客户端订单查询列表")
    @GetMapping("/client/list")
    public R clientList(@ParameterObject Page page, @ParameterObject TmsCustomerOrderPageVo vo) {
        return R.ok(tmsCustomerOrderService.clientList(page,vo));
    }


     // 客户端轨迹查询--中大件版本(暂时废弃)
     @Operation(summary = "客户端轨迹查询", description = "客户端轨迹查询")
     @SysLog("客户端轨迹查询" )
     @PostMapping("/client/track")
     public R track(@RequestParam("orderNo") String orderNo) {
         return tmsCustomerOrderService.getTrack(orderNo);
     }

     // 根据箱号查询对应轨迹节点记录--中大件版本(暂时废弃)
     @Operation(summary = "根据箱号查询对应轨迹节点记录", description = "根据箱号查询对应轨迹节点记录")
     @GetMapping("/track/list/{subOrderNo}")
     public R trackList(@PathVariable("subOrderNo") String subOrderNo) {
         return tmsCustomerOrderService.getTrackList(subOrderNo);
     }

    // 轨迹查询--中大件版本
    @Operation(summary = "中大件-轨迹查询", description = "中大件-轨迹查询")
    @SysLog("中大件-轨迹查询" )
    @PostMapping("/zdj/getTrack")
    public R getZdjTrack(@RequestBody TmsOrderNoTrackVo orderNos) {
        return tmsCustomerOrderService.getZdjTrack(orderNos);
    }

    // 根据箱号查询对应轨迹节点记录--中大件版本
    @Operation(summary = "中大件-根据箱号查询对应轨迹节点记录", description = "中大件-根据箱号查询对应轨迹节点记录")
    @GetMapping("zdj/track/list/{subOrderNo}")
    public R getZdjTrackList(@PathVariable("subOrderNo") String subOrderNo) {
        return tmsCustomerOrderService.getZdjTrackList(subOrderNo);
    }

    // 轨迹查询--官网中大件版本
    @ReCaptchaCheck
    @Inner(value = false)
    @Operation(summary = "官网中大件版本-轨迹查询", description = "官网中大件版本-轨迹查询")
    @SysLog("官网中大件版本-轨迹查询" )
    @PostMapping("/zdj/web/getTrack")
    public R getZdjWebTrack(@RequestBody TmsWebOrderTrackVo vo) {
        return tmsCustomerOrderService.getZdjWebTrackNew(vo);
    }

    // 轨迹查询--官网中大件版本-人机校验不通过
    @Inner(value = false)
    @Operation(summary = "官网中大件版本-轨迹查询-人机校验不通过", description = "官网中大件版本-轨迹查询-人机校验不通过")
    @SysLog("官网中大件版本-轨迹查询-人机校验不通过" )
    @PostMapping("/zdj/web/getTrackNoCaptcha")
    public R getZdjWebTrackNoCaptcha(@RequestBody TmsWebOrderTrackVo vo) {
        return tmsCustomerOrderService.getZdjWebTrackNew(vo);
    }

    /**
     * 导入订单 -- xls上传订单
     * @param file
     * @return
     */
    @Operation(summary = "导入订单" , description = "导入订单" )
    @SysLog("导入订单" )
    @PostMapping("/_import")
    @Inner(value = false)
    public R importOrders(@RequestParam("file") MultipartFile file, @RequestParam("customerId") Long customerId) throws IOException {
        return tmsCustomerOrderService.processFile(file,customerId);
    }

    @Operation(summary = "订单修改POD", description = "订单修改POD")
    @SysLog("订单修改POD" )
    @PostMapping("/updatePOD")
    public R updatePod(String orderNo,String picUrl) {
        return tmsCustomerOrderService.updatePod(orderNo,picUrl);
    }

    @Operation(summary = "面单打印校验", description = "面单打印校验")
    @PostMapping("/labelCheckout")
    public R labelCheckout(@RequestParam String orderNo) {
        return tmsCustomerOrderService.labelCheckout(orderNo);
    }

    // 根据订单号查询订单日志
    @Operation(summary = "根据订单号查询订单日志", description = "根据订单号查询订单日志")
    @GetMapping("/orderLogList")
    public R orderLogList(@RequestParam String orderNo) {
        return tmsCustomerOrderService.getOrderLog(orderNo);
    }



    /**
     * 根据跟踪单号查询订单
     */
    @Inner(value = false)
    @NoToken
    @Operation(summary = "根据跟踪单号查询订单" , description = "根据跟踪单号查询订单" )
    @GetMapping("/getCustomerOrderByOrderNo" )
    public TmsCustomerOrderEntity getCustomerOrderByOrderNo(@RequestParam("orderNo") String orderNo) {
        return tmsCustomerOrderService.getCustomerOrderByOrderNo(orderNo);
    }





    /**
     * 分拣机分拣后将订单状态改为已入库（加了一个字段）-并保存轨迹
     */
    @Inner(value = false)
    @Operation(summary = "根据跟踪单号查询订单" , description = "根据跟踪单号查询订单" )
    @PostMapping("/sortingUpdate" )
    public R sortingUpdate(@RequestParam("orderNo") String orderNo) {
        return tmsCustomerOrderService.sortingUpdate(orderNo);
    }

    /**
     * 分拣机扫码-并保存轨迹
     */
    @Inner(value = false)
    @Operation(summary = "根据跟踪单号查询订单" , description = "根据跟踪单号查询订单" )
    @PostMapping("/sortingOperated" )
    public R sortingOperated(@RequestParam("entrustedOrderNumbers") List<String> entrustedOrderNumbers) {
        tmsCustomerOrderService.sortingOperated(entrustedOrderNumbers);
        return R.ok();
    }

    /**
     * 新增分拣机复核体积和重量
     */
    @Inner(value = false)
    @SysLog("新增分拣机复核体积和重量" )
    @Operation(summary = "新增分拣机复核体积和重量", description = "新增分拣机复核体积和重量")
    @PostMapping("/addReviewVolumeAndWeight")
    public R addReviewVolumeAndWeight(@RequestParam("volume") BigDecimal volume,
                                      @RequestParam("weight") BigDecimal weight, @RequestParam("orderNo") String orderNo) {
        return R.ok(tmsCustomerOrderService.addReviewVolumeAndWeight(volume, weight, orderNo));
    }



    /**
     * 合并面单
     */
    @Operation(summary = "合并面单" , description = "合并面单" )
    @PostMapping("/downloadMergedPdf")
    public ResponseEntity<byte[]> downloadMergedPdf(String ids) throws Exception {
     return  tmsCustomerOrderService.mergeLabel(ids);
    }

    /**
     * 保存订单批次数据
     */
    @Inner(value = false)
    @SysLog("保存订单批次数据" )
    @Operation(summary = "保存订单批次数据", description = "保存订单批次数据")
    @PostMapping("/saveBatchOrder")
    public Boolean saveBatchOrder(@RequestParam("orderNo") String orderNo) {
        return tmsCustomerOrderService.saveBatchOrder(orderNo);
    }

//    // 官网中大件轨迹获取验证码
//    @Inner(value = false)
//    @Operation(summary = "官网中大件轨迹获取验证码", description = "官网中大件轨迹获取验证码")
//    @SysLog("官网中大件轨迹获取验证码" )
//    @PostMapping("/zdj/web/getCaptcha")
//    public R getCaptcha(@RequestBody TmsWebOrderTrackVo vo) {
//        return tmsCustomerOrderService.getCaptcha(vo.getOrderList());
//    }
//
//    // 官网中大件轨迹校验验证码
//    @Inner(value = false)
//    @Operation(summary = "官网中大件轨迹校验验证码", description = "官网中大件轨迹校验验证码")
//    @SysLog("官网中大件轨迹校验验证码" )
//    @PostMapping("/zdj/web/verifyCode")
//    public R verifyCode(@RequestBody TmsWebOrderTrackVo vo) {
//        return tmsCustomerOrderService.getCaptcha(vo.getCaptcha(), vo.getOrderList());
//    }

}
