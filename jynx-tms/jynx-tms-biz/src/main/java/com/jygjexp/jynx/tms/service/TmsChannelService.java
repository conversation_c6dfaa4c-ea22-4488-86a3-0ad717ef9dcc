package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsChannel;
import com.jygjexp.jynx.tms.excel.TmsChannelExcelDto;

import java.util.List;

public interface TmsChannelService extends IService<TmsChannel> {

    /**
     * 分页
     *
     * @param page
     * @param tmsChannel
     * @return
     */
    Page<TmsChannel> search(Page page, TmsChannel tmsChannel);

    /**
     * 导出
     *
     * @param page
     * @param tmsChannel
     * @return
     */
    List<TmsChannelExcelDto> export(Page page, TmsChannel tmsChannel);

    /**
     * 启用
     *
     * @param id
     * @return
     */
    boolean enable(Long id);

    /**
     * 禁用
     *
     * @param id
     * @return
     */
    Boolean disable(Long id);

    /**
     * 获取所有
     * @return
     */
    List<TmsChannel> channels();
}