package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.dto.TmsOrderBatchDto;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsOrderBatchEntity;
import com.jygjexp.jynx.tms.entity.TmsOverAreaEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsOrderBatchMapper;
import com.jygjexp.jynx.tms.mapper.TmsOverAreaMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsOrderBatchService;
import com.jygjexp.jynx.tms.vo.TmsCustomerOrderBatchVo;
import com.jygjexp.jynx.tms.vo.TmsOrderBatchPageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批次管理
 *
 * <AUTHOR>
 * @date 2025-05-13 15:19:16
 */
@Service
@RequiredArgsConstructor
public class TmsOrderBatchServiceImpl extends ServiceImpl<TmsOrderBatchMapper, TmsOrderBatchEntity> implements TmsOrderBatchService {
    private final TmsOrderBatchMapper tmsOrderBatchMapper;
    private final TmsCustomerOrderMapper tmsCustomerOrderMapper;
    private final TmsOverAreaMapper tmsOverAreaMapper;
    private final RemoteUserService remoteUserService;

    /**
     * 批次管理分页
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<TmsOrderBatchPageVo> search(Page page, TmsOrderBatchPageVo vo) {
        MPJLambdaWrapper<TmsOrderBatchEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsOrderBatchEntity.class)
                // 批次号
                .like(StrUtil.isNotBlank(vo.getBatchNo()), TmsOrderBatchEntity::getBatchNo, vo.getBatchNo())
                // 创建时间
                .between(ObjectUtil.isNotNull(vo.getBeginTime()) && ObjectUtil.isNotNull(vo.getEndTime()),
                        TmsOrderBatchEntity::getCreateTime, vo.getBeginTime(), vo.getEndTime())
                .orderByDesc(TmsOrderBatchEntity::getCreateTime);
        return tmsOrderBatchMapper.selectPage(page, wrapper);
    }

    /**
     * 创建批次
     * @param dto
     * @return
     */
    @Override
    public R addOrderBatch(TmsOrderBatchDto dto) {
        TmsOrderBatchEntity orderBatch = new TmsOrderBatchEntity();
        orderBatch.setBatchNo(dto.getBatchNo());

        //添加一个批次时间，用于路径规划的批次时间判断，勿删！
        orderBatch.setBatchTime(LocalDateTime.now());

        // 查询输入的批次号在系统中是否存在
        TmsOrderBatchEntity batch = tmsOrderBatchMapper.selectOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, dto.getBatchNo()));
        if (ObjectUtil.isNotNull(batch)) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.batchno.repeat", null));
        }

        List<String> mainOrderNos = dto.getEntrustedOrderNos();
        // 订单数(在分拣管理中选择的订单数量)
        orderBatch.setOrderCount(mainOrderNos.size());

        // 一次性查询所有相关订单（包括子单和主单）
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(wq -> {
            mainOrderNos.forEach(mainNo ->
                    wq.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo)
            );
        });
        List<TmsCustomerOrderEntity> allOrders = tmsCustomerOrderMapper.selectList(wrapper);

        // 提取所有真实的订单号（包含子单）
        Set<String> allOrderNos = allOrders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toSet());
        // 提取所有主单
        List<TmsCustomerOrderEntity> mainOrders = allOrders.stream().filter(order -> order.getSubFlag().equals(Boolean.FALSE)).collect(Collectors.toList());
        Map<String, String> mainOrderRouteMap = new HashMap<>();
        for (TmsCustomerOrderEntity mainOrder : mainOrders) {
            TmsOverAreaEntity overAreaByZipCode = getOverAreaByZipCode(mainOrder.getDestPostalCode().substring(0, 3));
            if (ObjectUtil.isNull(overAreaByZipCode)) {
                throw new CustomBusinessException(LocalizedR.getMessage("tms.null.area.order.batchno", null));
            }
            // 设置路线编号
            String routeNumber = overAreaByZipCode.getRouteNumber();
            mainOrder.setRouteNumber(routeNumber);
            mainOrderRouteMap.put(mainOrder.getEntrustedOrderNumber(), routeNumber);
        }
//        tmsCustomerOrderMapper.updateById(mainOrders);
        mainOrders.forEach(tmsCustomerOrderMapper::updateById);

        // 设置子单的路线编号
        List<TmsCustomerOrderEntity> subOrders = allOrders.stream().filter(order -> order.getSubFlag().equals(Boolean.TRUE)).collect(Collectors.toList());

        for (TmsCustomerOrderEntity subOrder : subOrders) {
            String parentMainOrderNo = mainOrderRouteMap.keySet().stream()
                    .filter(mainNo -> subOrder.getEntrustedOrderNumber().startsWith(mainNo))
                    .findFirst()
                    .orElse(null);

            if (parentMainOrderNo != null) {
                subOrder.setRouteNumber(mainOrderRouteMap.get(parentMainOrderNo));
                tmsCustomerOrderMapper.updateById(subOrder);
            }
        }

        // 提取目的地邮编前三位（去重）
        List<String> postCodes = allOrders.stream()
                .map(TmsCustomerOrderEntity::getDestPostalCode)
                .filter(StrUtil::isNotBlank)
                .map(code -> code.replaceAll("\\s+", ""))
                .filter(code -> code.length() >= 3)
                .map(code -> code.substring(0, 3).toUpperCase())
                .distinct()
                .collect(Collectors.toList());

        // 通过覆盖邮编查询覆盖区域
        LambdaQueryWrapper<TmsOverAreaEntity> qw = new LambdaQueryWrapper<>();
        qw.and(wq -> {
            postCodes.forEach(zip -> {wq.or().like(TmsOverAreaEntity::getZip, zip);
            });
        });

        // 区域数
        List<TmsOverAreaEntity> overAreas = tmsOverAreaMapper.selectList(qw);
        orderBatch.setAreaCount(overAreas.size());

        // 未送达数
        LambdaQueryWrapper<TmsCustomerOrderEntity> notDeliveredQw = new LambdaQueryWrapper<>();
        notDeliveredQw.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNos)
                .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode());
        orderBatch.setNonDeliveryCount(Math.toIntExact(tmsCustomerOrderMapper.selectCount(notDeliveredQw)));

        // 已送达数
        LambdaQueryWrapper<TmsCustomerOrderEntity> deliveredQw = new LambdaQueryWrapper<>();
        deliveredQw.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNos)
                .eq(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode());
        orderBatch.setDeliveredCount(Math.toIntExact(tmsCustomerOrderMapper.selectCount(deliveredQw)));

        // 保存批次
        boolean save = this.save(orderBatch);
        if (!save) {
            return R.failed(Boolean.FALSE);
        }
        // 修改建立批次为是，并设置批次号到订单中
        LambdaUpdateWrapper<TmsCustomerOrderEntity> upWrapper = new LambdaUpdateWrapper<>();
        upWrapper.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, allOrderNos)
                .set(TmsCustomerOrderEntity::getIsBatch, true)
                .set(TmsCustomerOrderEntity::getBatchNo, orderBatch.getBatchNo());   // 设置路线编号到订单中
        tmsCustomerOrderMapper.update(upWrapper);

        return LocalizedR.ok("tms.orderbatch.created.successful", "");
    }


    // 创建空批次
    @Override
    public R createOrderBatch(TmsOrderBatchDto dto) {
        TmsOrderBatchEntity orderBatch = new TmsOrderBatchEntity();
        orderBatch.setBatchNo(dto.getBatchNo());

        // 查询输入的批次号在系统中是否存在
        TmsOrderBatchEntity batch = tmsOrderBatchMapper.selectOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, dto.getBatchNo()));
        if (ObjectUtil.isNotNull(batch)) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.batchno.repeat", null));
        }
        orderBatch.setOrderCount(0);
        orderBatch.setAreaCount(0);
        orderBatch.setNonDeliveryCount(0);
        orderBatch.setDeliveredCount(0);
        orderBatch.setBatchTime(LocalDateTime.now());

        // 保存批次
        boolean save = this.save(orderBatch);
        if (!save) {
            return R.failed(Boolean.FALSE);
        }
        return LocalizedR.ok("tms.orderbatch.created.successful", "");
    }

    // 分拣订单选择批次
    @Override
    public R selectOrderBatch(TmsOrderBatchDto dto) {
        // 查询输入的批次号在系统中是否存在
        TmsOrderBatchEntity batch = tmsOrderBatchMapper.selectOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, dto.getBatchNo()),false);
        if (ObjectUtil.isNull(batch)) {
            return R.failed(LocalizedR.getMessage("batch.not_exists", null));
        }

        List<String> mainOrderNos = dto.getEntrustedOrderNos();
        // 去重mainOrderNos
        mainOrderNos = mainOrderNos.stream().distinct().collect(Collectors.toList());
        // 向原批次中添加相应订单数(在分拣管理中选择的订单数量)
        batch.setOrderCount(Optional.ofNullable(batch.getOrderCount()).orElse(0) + mainOrderNos.size());

        // 一次性查询所有相关订单（主单）
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getBatchNo, batch.getBatchNo());
        wrapper.in(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE);
        List<TmsCustomerOrderEntity> allOrders = tmsCustomerOrderMapper.selectList(wrapper);

        // 提取所有主单
        List<TmsCustomerOrderEntity> mainOrders = allOrders.stream().filter(order -> Boolean.FALSE.equals(order.getSubFlag())).collect(Collectors.toList());

        // 获取新传入订单号列表
        List<String> inputMainOrderNos = dto.getEntrustedOrderNos();

        // 获取 mainOrders 的主单号（数据库查出来的）
        Set<String> dbMainOrderNos = mainOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toSet());

        // 合并传入订单和数据库中查到的主单号，去重
        Set<String> totalOrderNos = new HashSet<>(inputMainOrderNos);
        totalOrderNos.addAll(dbMainOrderNos);

        // 查询区域数（以这些订单为基础的 count 数）
        LambdaQueryWrapper<TmsCustomerOrderEntity> newWrapper = new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, totalOrderNos);
        //此时该批次的所有订单
        List<TmsCustomerOrderEntity> allCustomerOrderList = tmsCustomerOrderMapper.selectList(newWrapper);
        //该批次订单的所有路线号去重
        Set<String> routeNumbers = allCustomerOrderList.stream().map(TmsCustomerOrderEntity::getRouteNumber).collect(Collectors.toSet());
        // 设置区域数
        batch.setAreaCount(routeNumbers.size());

        // 未送达数
        LambdaQueryWrapper<TmsCustomerOrderEntity> notDeliveredQw = new LambdaQueryWrapper<>();
        notDeliveredQw.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, inputMainOrderNos)
                .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode());
        batch.setNonDeliveryCount(Optional.ofNullable(batch.getNonDeliveryCount()).orElse(0)+Math.toIntExact(tmsCustomerOrderMapper.selectCount(notDeliveredQw)));

        // 已送达数
        LambdaQueryWrapper<TmsCustomerOrderEntity> deliveredQw = new LambdaQueryWrapper<>();
        deliveredQw.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, inputMainOrderNos)
                .eq(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode());
        batch.setDeliveredCount(Optional.ofNullable(batch.getDeliveredCount()).orElse(0)+Math.toIntExact(tmsCustomerOrderMapper.selectCount(deliveredQw)));

        // 更新批次
        boolean save = this.updateById(batch);
        if (!save) {
            return R.failed(Boolean.FALSE);
        }
        // 修改建立批次为是，并设置批次号到订单中
        LambdaUpdateWrapper<TmsCustomerOrderEntity> upWrapper = new LambdaUpdateWrapper<>();
        upWrapper.and(wrapper1 -> {
                    for (String mainNo : inputMainOrderNos) {
                        wrapper1.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
                    }
                })
                .set(TmsCustomerOrderEntity::getIsBatch, true)
                .set(TmsCustomerOrderEntity::getBatchNo, batch.getBatchNo());

        tmsCustomerOrderMapper.update(upWrapper);

        return LocalizedR.ok("tms.orderbatch.select.successful", "");
    }

    /**
     * 批次跟踪单详情
     * @param batchNo
     * @return
     */
    @Override
    public Page<TmsCustomerOrderBatchVo> getDetailByBatchNo(Page page, String batchNo) {
        MPJLambdaWrapper<TmsOrderBatchEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsCustomerOrderEntity.class)
                .select(TmsOrderBatchEntity::getId,
                        TmsOrderBatchEntity::getBatchNo,
                        TmsOrderBatchEntity::getAreaCount,
                        TmsOrderBatchEntity::getOrderCount,
                        TmsOrderBatchEntity::getNonDeliveryCount,
                        TmsOrderBatchEntity::getDeliveredCount,
                        TmsOrderBatchEntity::getCreateTime)

                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getBatchNo, TmsOrderBatchEntity::getBatchNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                .orderByDesc(TmsCustomerOrderEntity::getCreateTime)
                .eq(TmsCustomerOrderEntity::getBatchNo, batchNo);
        return tmsOrderBatchMapper.selectJoinPage(page, TmsCustomerOrderBatchVo.class, wrapper);
    }

    /**
     * 修改客户订单批次号
     * @param tmsOrderBatch
     * @return
     */
    @Override
    public R updateBatchNo(TmsOrderBatchEntity tmsOrderBatch, String oldBatchNo) {
        // 1. 新旧批次号是否一样，避免重复更新
        String newBatchNo = tmsOrderBatch.getBatchNo();
        if (StrUtil.isBlank(newBatchNo) || newBatchNo.equals(oldBatchNo)) {
            boolean batchUpdated = this.updateById(tmsOrderBatch);
            if (batchUpdated) {
                return R.ok(Boolean.TRUE);
            }
        }

        // 2. 查询旧批次号绑定的订单
        LambdaQueryWrapper<TmsCustomerOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(TmsCustomerOrderEntity::getBatchNo, oldBatchNo);
        List<TmsCustomerOrderEntity> orderList = tmsCustomerOrderMapper.selectList(qw);
        if (CollectionUtil.isNotEmpty(orderList)) {
            // 3. 获取所有订单号
            List<String> orderNos = orderList.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

            // 4. 执行客户订单表的更新
            LambdaUpdateWrapper<TmsCustomerOrderEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos)
                    .set(TmsCustomerOrderEntity::getBatchNo, newBatchNo);
            int updatedCount = tmsCustomerOrderMapper.update(wrapper);
            //return R.ok(updatedCount > 0, "Successfully updated the number of customer orders：" + updatedCount);
        }
        // 5. 更新批次表
        boolean batchUpdated = this.updateById(tmsOrderBatch);
        if (!batchUpdated) {
            return LocalizedR.failed("tms.orderbatch.updated.failed", "");
        }
        return R.ok(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsOrderBatchEntity createForwardBatch() {
        // 获取当前时分秒
        LocalDateTime now = LocalDateTime.now();
        String batchNo;

        // 循环校验直到找到可用的批次号
        do {
            batchNo = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));

            // 校验批次号是否存在
            LambdaQueryWrapper<TmsOrderBatchEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmsOrderBatchEntity::getBatchNo, batchNo);

            if (this.count(wrapper) > 0) {
                // 如果存在，时间加1分钟继续校验
                now = now.plusMinutes(1);
            } else {
                break;
            }
        } while (true);
        String port = getCurrentUserPort();

        // 判断其他批次状态
        List<TmsOrderBatchEntity> usingTemplates = list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getCheckForwardUsing, 1));
        if (CollUtil.isNotEmpty(usingTemplates)){
            usingTemplates.forEach(template -> {
                template.setCheckForwardUsing(0);
            });
            updateBatchById(usingTemplates);
        }

        // 创建批次
        TmsOrderBatchEntity batch = new TmsOrderBatchEntity();
        batchNo = port + "BAT" + batchNo;
        batch.setBatchNo(batchNo);
        batch.setOrderCount(0);
        batch.setAreaCount(0);
        batch.setNonDeliveryCount(0);
        batch.setDeliveredCount(0);
        batch.setIsScaning(1);
        batch.setCheckForwardUsing(1);
        save(batch);

        return batch;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeBatch() {
        // 关闭正向批次
        List<TmsOrderBatchEntity> usingTemplates = list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getCheckForwardUsing, 1));
        if (CollUtil.isNotEmpty(usingTemplates)){
            usingTemplates.forEach(template -> {
                template.setIsScaning(0);
                template.setCheckForwardUsing(0);
            });
            updateBatchById(usingTemplates);
        }
    }

    //根据邮编查询仓库ID
    public TmsOverAreaEntity getOverAreaByZipCode(String zipCode) {
        LambdaQueryWrapper<TmsOverAreaEntity> wrapper = new LambdaQueryWrapper<>();
        // 精确匹配：邮编在开头、中间或结尾，且前后有逗号或边界
//                .apply("zip REGEXP CONCAT('(^|,)', {0}, '(,|$)')", zipCode)
        wrapper.apply("FIND_IN_SET({0}, REPLACE(REPLACE(zip, ' ', ''), '\\t', '')) > 0", zipCode.trim())
                .eq(TmsOverAreaEntity::getIsValid, true)
                .last("limit 1");
        TmsOverAreaEntity overArea = tmsOverAreaMapper.selectOne(wrapper);
        return overArea;
    }

    // 获取当前用户的口岸信息
    private String getCurrentUserPort(){
        Long userId = SecurityUtils.getUser().getId();
        SysUser user = remoteUserService.getOneUserById(userId).getData();
        if (user == null || user.getPort() == null){
            return "";
        }
        if (user.getPort() == 0){
            return "T";
        }else if (user.getPort() == 1){
            return "V";
        }
        return "";
    }
}