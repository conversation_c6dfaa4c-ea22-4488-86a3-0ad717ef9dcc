package com.jygjexp.jynx.tms.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jygjexp.jynx.tms.annotation.DictKey;
import com.jygjexp.jynx.tms.converter.DictConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @date 2025-08-22 07:05:32
 */
@Data
@ExcelIgnoreUnannotated
public class TmsChannelExcelDto {

    /**
     * 渠道代码
     */
    @ExcelProperty("{tms.channel.channel.code}")
    @Schema(description = "渠道代码")
    private String channelCode;

    /**
     * 渠道名称
     */
    @ExcelProperty("{tms.channel.channel.name}")
    @Schema(description = "渠道名称")
    private String channelName;

    /**
     * 渠道类型 0:中大件配送
     */
    @DictKey("tms_channel_type")
    @ExcelProperty(value = "{tms.channel.channel.type}", converter = DictConverter.class)
    @Schema(description = "渠道类型 0:中大件配送")
    private Integer channelType;

    /**
     * 货物属性
     */
    @DictKey("tms_goods_type")
    @ExcelProperty(value = "{tms.channel.goods.type}", converter = DictConverter.class)
    @Schema(description = "货物属性 0:普通货物;1:危险货物")
    private Integer goodsType;

    /**
     * 时效
     */
    @ExcelProperty("{tms.channel.time.lineless}")
    @Schema(description = "时效")
    private Integer timeliness;

    /**
     * 材积除
     */
    @ExcelProperty("{tms.channel.volume.weight}")
    @Schema(description = "材积除")
    private Integer volumetricWeight;

    /**
     * 计费节点 1:固定
     */
    @DictKey("tms_billing_node")
    @ExcelProperty(value = "{tms.channel.billing.node}", converter = DictConverter.class)
    @Schema(description = "计费节点 1:包裹入仓\\分拣")
    private Integer billingNode;

    /**
     * 服务商
     */
    @ExcelProperty("{tms.channel.service.provider}")
    @Schema(description = "服务商")
    private String serviceProvider;

    /**
     * 可选分区
     */
    @ExcelProperty("{tms.channel.reachable.area}")
    @Schema(description = "可选分区")
    private String reachableArea;

    /**
     * 不可达分区
     */
    @ExcelProperty("{tms.channel.unreachable.area}")
    @Schema(description = "不可达分区")
    private String unreachableArea;
}