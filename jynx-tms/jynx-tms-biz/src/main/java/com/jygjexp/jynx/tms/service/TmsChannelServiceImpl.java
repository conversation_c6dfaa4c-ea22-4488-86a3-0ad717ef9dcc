package com.jygjexp.jynx.tms.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.entity.TmsChannel;
import com.jygjexp.jynx.tms.excel.TmsChannelExcelDto;
import com.jygjexp.jynx.tms.mapper.TmsChannelMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @date 2025-08-22 07:06:55
 */
@Service
public class TmsChannelServiceImpl extends ServiceImpl<TmsChannelMapper, TmsChannel> implements TmsChannelService {

    /**
     * 分页
     *
     * @param page
     * @param tmsChannel
     * @return
     */
    @Override
    public Page<TmsChannel> search(Page page, TmsChannel tmsChannel) {
        LambdaQueryWrapper<TmsChannel> wrapper = Wrappers.lambdaQuery(TmsChannel.class)
                .like(StrUtil.isNotBlank(tmsChannel.getChannelCode()), TmsChannel::getChannelCode, tmsChannel.getChannelCode())
                .like(StrUtil.isNotBlank(tmsChannel.getChannelName()), TmsChannel::getChannelName, tmsChannel.getChannelName())
                .eq(ObjUtil.isNotNull(tmsChannel.getStatus()), TmsChannel::getStatus, tmsChannel.getStatus())
                .orderByDesc(TmsChannel::getCreateTime);
        return page(page, wrapper);
    }

    /**
     * 导出
     *
     * @param page
     * @param tmsChannel
     * @return
     */
    @Override
    public List<TmsChannelExcelDto> export(Page page, TmsChannel tmsChannel) {
        return BeanUtil.copyToList(search(page, tmsChannel).getRecords(), TmsChannelExcelDto.class);
    }

    /**
     * 启用
     *
     * @param id
     * @return
     */
    @Override
    public boolean enable(Long id) {
        TmsChannel entity = new TmsChannel();
        entity.setId(id);
        entity.setStatus(1);
        return updateById(entity);
    }

    /**
     * 禁用
     *
     * @param id
     * @return
     */
    @Override
    public Boolean disable(Long id) {
        TmsChannel entity = new TmsChannel();
        entity.setId(id);
        entity.setStatus(0);
        return updateById(entity);
    }

    /**
     * 获取所有
     *
     * @return
     */
    @Override
    public List<TmsChannel> channels() {
        LambdaQueryWrapper<TmsChannel> wrapper = Wrappers.lambdaQuery(TmsChannel.class)
                .eq(TmsChannel::getStatus, 1);
        return list(wrapper);
    }
}